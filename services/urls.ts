const buildURL = (basePath: string) => (endpoint: string) => `${basePath}${endpoint}`;

const pollURL = buildURL("/jusvote/manage-event");
const teamURL = buildURL("/jusvote/manage-team");
const buildVerseURL = buildURL("/buildverse");

const jusVoteUrls = {
    listTeams: (eventId: string) => pollURL(`/${eventId}/list-teams/`),
    updateTeamStatus: (teamId: string) => teamURL(`/${teamId}/update-team-status/`),
    getQuestions: (eventId: string) => pollURL(`/${eventId}/get-questions/`),
    submitPoll: (teamId: string) => teamURL(`/${teamId}/vote/`),
    listPolls: (eventId: string, teamId: string) =>
        teamURL(`/${eventId}/team/${teamId}/vote-results/`),
    generateJWT: (eventId: string) => pollURL(`/${eventId}/generate-jwt/`),
    getEventId: (eventName: string) => pollURL(`/${eventName}/get-event-id/`),
    onboardUser: pollURL(`/onboard-user/`),
    listEvents: pollURL(`/list-events/`),
    getEventHosts: (eventId: string) => pollURL(`/event/${eventId}/get-event-hosts/`),
    refreshEventHosts: (mmpEventId: string) => pollURL(`/event/${mmpEventId}/refresh-event-hosts/`),
    getLeaderboard: (eventId: string) => pollURL(`/${eventId}/leaderboard/`),
    // Event CRUD operations
    createEvent: pollURL(`/create/`),
    getEvent: (eventId: string) => pollURL(`/${eventId}/update/`),
    updateEvent: (eventId: string) => pollURL(`/${eventId}/update/`),
    deleteEvent: (eventId: string) => pollURL(`/${eventId}/delete/`),
};

export const buildVerse = {
    login: buildVerseURL("/login/"),
    getAccessToken: buildVerseURL("/get-access-token/"),
    generateOTP: buildVerseURL("/generate-otp/"),
    preRegister: buildVerseURL("/pre-register/"),
    register: buildVerseURL("/register/"),
    updateProfile: buildVerseURL("/update-profile/"),
    profileInfo: buildVerseURL("/profile-info/"),
    setUserData: (token: string) => buildVerseURL(`/set-user-data/${token}`),
    googleLogin: buildVerseURL("/auth/google/"),
    resetPassword: buildVerseURL("/reset-password/"),
    updateProfilePassword: buildVerseURL("/change-password/"),
};

export { jusVoteUrls as pollUrls };

const pollStatusWS = (eventId: string) => `common/${eventId}/status/`;

export { pollStatusWS };
