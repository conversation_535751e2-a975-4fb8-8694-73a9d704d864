{"name": "poll", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.2", "axios": "^1.7.9", "framer-motion": "^11.14.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-router-dom": "^7.0.2", "react-select": "^5.9.0", "react-spinners": "^0.15.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.3"}}