import toast from "react-hot-toast";
import { pollUrls } from "../../services/urls";
import { privateGateway, publicGateway } from "../../services/apiGateways";
import { Dispatch, SetStateAction } from "react";

// Function to get questions for a specific event
export const getQuestions = (
    eventId: string,
    setQuestions: React.Dispatch<React.SetStateAction<any>>
) => {
    privateGateway
        .get(pollUrls.getQuestions(eventId))
        .then((response) => {
            setQuestions(response.data.response.questions); // Update the state with the response data
        })
        .catch((error) => {
            console.error("Error fetching questions:", error);
            toast.error("Failed to fetch questions"); // Show error toast
        });
};

export const submitPoll = (
    teamId: string,
    pollData: any,
    setIsLoading: Dispatch<SetStateAction<boolean>>,
    setAlreadySubmitted: Dispatch<SetStateAction<boolean>>,
    setShowVerificationModal: Dispatch<SetStateAction<boolean>>
) => {
    setIsLoading(true);
    publicGateway
        .post(
            pollUrls.submitPoll(teamId),
            {
                submissions: pollData.scores,
            },
            {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
                },
            }
        )
        .then(() => {
            toast.success("Poll submitted successfully"); // Show success toast
            setAlreadySubmitted(true); // Set already submitted state to true
            setShowVerificationModal(false); // Close the verification modal
        })
        .catch((error) => {
            console.error("Error submitting poll:", error);
            toast.error(error.response.data.message.general[0]); // Show error toast
            // setAlreadySubmitted(true); // Set already submitted state to true
        })
        .finally(() => {
            setIsLoading(false); // Set loading state to false
        });
};

export const getEventId = (
    eventName: string,
    setEventId: Dispatch<SetStateAction<string | undefined>>,
    setEventNotFound?: Dispatch<SetStateAction<boolean>>
) => {
    publicGateway
        .get(pollUrls.getEventId(eventName))
        .then((response) => {
            setEventId(response.data.response.event_id);
        })
        .catch((error) => {
            console.error("Error fetching event ID:", error);
            toast.error("Event Not Found", {
                id: "event-id-missing",
            });
            if (setEventNotFound) setEventNotFound(true);
        });
};
