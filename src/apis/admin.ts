import toast from "react-hot-toast";
import { pollUrls } from "../../services/urls";
import { privateGateway } from "../../services/apiGateways";
import { Mode, Team } from "../types/idea";
import { PollResult } from "../types/poll";
import { Event, CreateEventRequest, UpdateEventRequest } from "../types/event";
// Function to list teams based on event_id
export const listTeams = (
    eventId: string,
    setTeams: React.Dispatch<React.SetStateAction<Team[]>>,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
    setIsLoading(true); // Set the loading state to true
    privateGateway
        .get(pollUrls.listTeams(eventId))
        .then((response) => {
            setTeams(response.data.response); // Update the state with the response data
        })
        .catch((error) => {
            console.error("Error listing teams:", error);
            toast.error("Failed to fetch teams", { id: "fetch-teams-error" }); // Show error toast
        })
        .finally(() => {
            setIsLoading(false); // Set the loading state to false
        });
};

// Function to update the status of a team based on team_id
export const updateTeamStatus = (
    teamId: string,
    status: Mode,
    teams: Team[],
    setTeams: React.Dispatch<React.SetStateAction<Team[]>>,
    id: number,
    setTimer?: React.Dispatch<React.SetStateAction<number | null>>
) => {
    privateGateway
        .post(pollUrls.updateTeamStatus(teamId), { status })
        .then(() => {
            toast.success(`Team status updated to ${status}`, {
                id: "update-team-status-success",
            });

            if (status === "Idle") {
                localStorage.removeItem("adminVotingStartingTime");
                return;
            }

            if (status === "Voting") {
                localStorage.setItem("adminVotingStartingTime", Date.now().toString());
                setTimer?.(303);
            }

            setTeams(teams.map((idea) => (idea.id === id ? { ...idea, status: status } : idea)));
        })
        .catch((error) => {
            console.error("Error updating team status:", error);
            toast.error("Failed to update team status", {
                id: "update-team-status-error",
            }); // Show error toast
        });
};

export const getPolls = (
    eventId: string,
    teamId: string,
    setPolls: React.Dispatch<React.SetStateAction<PollResult[] | undefined>>,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
    setIsLoading(true); // Set the loading state to true
    privateGateway
        .get(pollUrls.listPolls(eventId, teamId))
        .then((response) => {
            setPolls(response.data.response); // Update the state with the response data
        })
        .catch((error) => {
            console.error("Error listing polls:", error);
            toast.error("Failed to fetch polls", {
                id: "fetch-polls-error",
            }); // Show error toast
        })
        .finally(() => {
            setIsLoading(false); // Set the loading state to false
        });
};

export const listEvents = (
    setEvents: React.Dispatch<React.SetStateAction<any[]>>,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
    setIsLoading(true); // Set the loading state to true
    privateGateway
        .get(pollUrls.listEvents)
        .then((response) => {
            setEvents(response.data.response); // Update the state with the response data
        })
        .catch((error) => {
            console.error("Error listing events:", error);
            toast.error("Failed to fetch events", {
                id: "fetch-events-error",
            }); // Show error toast
        })
        .finally(() => {
            setIsLoading(false); // Set the loading state to false
        });
};

export const getLeaderboard = (
    eventId: string,
    setLeaderboard: React.Dispatch<React.SetStateAction<any[]>>,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
    setFilteredLeaderboard: React.Dispatch<React.SetStateAction<any[]>>
) => {
    setIsLoading(true); // Set the loading state to true
    privateGateway
        .get(pollUrls.getLeaderboard(eventId))
        .then((response) => {
            setLeaderboard(response.data.response); // Update the state with the response data
            setFilteredLeaderboard(response.data.response);
        })
        .catch((error) => {
            console.error("Error listing leaderboard:", error);
            toast.error("Failed to fetch leaderboard", {
                id: "fetch-leaderboard-error",
            }); // Show error toast
        })
        .finally(() => {
            setIsLoading(false); // Set the loading state to false
        });
};

// Event CRUD operations

// Function to create a new event
export const createEvent = (
    data: CreateEventRequest,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
    onSuccess?: (event: Event) => void
) => {
    setIsLoading(true);
    privateGateway
        .post(pollUrls.createEvent, data)
        .then((response) => {
            toast.success("Event created successfully", {
                id: "create-event-success",
            });
            if (onSuccess) {
                onSuccess(response.data.response);
            }
        })
        .catch((error) => {
            console.error("Error creating event:", error);
            toast.error("Failed to create event", {
                id: "create-event-error",
            });
        })
        .finally(() => {
            setIsLoading(false);
        });
};

// Function to get event details for editing
export const getEvent = (
    eventId: string,
    setEvent: React.Dispatch<React.SetStateAction<Event | null>>,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
    setIsLoading(true);
    privateGateway
        .get(pollUrls.getEvent(eventId))
        .then((response) => {
            setEvent(response.data.response);
        })
        .catch((error) => {
            console.error("Error fetching event:", error);
            toast.error("Failed to fetch event details", {
                id: "fetch-event-error",
            });
        })
        .finally(() => {
            setIsLoading(false);
        });
};

// Function to update an event
export const updateEvent = (
    eventId: string,
    data: UpdateEventRequest,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
    onSuccess?: (event: Event) => void
) => {
    setIsLoading(true);
    privateGateway
        .patch(pollUrls.updateEvent(eventId), data)
        .then((response) => {
            toast.success("Event updated successfully", {
                id: "update-event-success",
            });
            if (onSuccess) {
                onSuccess(response.data.response);
            }
        })
        .catch((error) => {
            console.error("Error updating event:", error);
            toast.error("Failed to update event", {
                id: "update-event-error",
            });
        })
        .finally(() => {
            setIsLoading(false);
        });
};

// Function to delete an event
export const deleteEvent = (
    eventId: string,
    setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
    onSuccess?: () => void
) => {
    setIsLoading(true);
    privateGateway
        .delete(pollUrls.deleteEvent(eventId))
        .then(() => {
            toast.success("Event deleted successfully", {
                id: "delete-event-success",
            });
            if (onSuccess) {
                onSuccess();
            }
        })
        .catch((error) => {
            console.error("Error deleting event:", error);
            toast.error("Failed to delete event", {
                id: "delete-event-error",
            });
        })
        .finally(() => {
            setIsLoading(false);
        });
};
