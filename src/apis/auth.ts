import { Dispatch } from "react";
import toast from "react-hot-toast";
import { NavigateFunction } from "react-router";

import { buildVerse, pollUrls } from "../../services/urls";
import { AuthFormFields } from "../pages/auth/types";
import { privateGateway, publicGateway } from "../../services/apiGateways";
import { TokenResponse } from "@react-oauth/google";

export const login = async ({
    email,
    setAuthFormFields,
    otp,
    password,
}: {
    email: string;
    setAuthFormFields: Dispatch<React.SetStateAction<AuthFormFields>>;
    otp?: string;
    password?: string;
}) => {
    setAuthFormFields((prev) => ({
        ...prev,
        isLoading: true,
    }));
    const data = {
        email_or_phone: email,
        ...(password ? { password: password } : { otp: otp }),
    };
    publicGateway
        .post(buildVerse.login, data)
        .then((response) => {
            const { access_token, refresh_token, profile_pic_url } = response.data.response;

            localStorage.setItem("accessToken", access_token);
            localStorage.setItem("refreshToken", refresh_token);
            localStorage.setItem("userEmail", email);
            if (profile_pic_url) {
                localStorage.setItem("userImage", profile_pic_url);
            }

            setAuthFormFields((prev) => ({
                ...prev,
                authenticated: true,
            }));
            privateGateway.post(pollUrls.onboardUser);
        })
        .catch((error) => {
            if (error.response.data.statusCode === 1001) {
                setAuthFormFields((prev) => ({
                    ...prev,
                    password: {
                        error: [],
                        showField: false,
                    },
                }));
                preRegister({ email, setAuthFormFields });
            } else {
                const errorMessage = error.response.data.message;
                setAuthFormFields((prev) => ({
                    ...prev,
                    email: {
                        error: errorMessage.email || undefined,
                        showField: true,
                    },
                    password: {
                        error: errorMessage.password || undefined,
                        showField: password ? true : false,
                    },
                    otp: {
                        error: errorMessage.otp || undefined,
                        showField: otp ? true : false,
                    },
                    generalMessage: errorMessage.general[0],
                    primaryButtonText: "Login",
                }));
            }
        })
        .finally(() => {
            setAuthFormFields((prev) => ({
                ...prev,
                isLoading: false,
            }));
        });
};

export const preRegister = async ({
    email,
    setAuthFormFields,
}: {
    email: string;
    setAuthFormFields: Dispatch<React.SetStateAction<AuthFormFields>>;
}) => {
    setAuthFormFields((prev) => ({
        ...prev,
        isLoading: true,
    }));
    publicGateway
        .post(buildVerse.preRegister, {
            email: email,
        })
        .then((response) => {
            toast.success(response.data.message.general[0]);
            setAuthFormFields((prev) => ({
                ...prev,
                email: {
                    error: [],
                    showField: true,
                },
                name: {
                    error: [],
                    showField: true,
                },
                otp: {
                    error: [],
                    showField: true,
                },
                api: {
                    name: "Register",
                    type: null,
                },
                resendOtp: {
                    apiName: "PreRegister",
                    apiType: "",
                    showButton: true,
                    timer: 15,
                },
                generalMessage: response.data.message.general[0],
                primaryButtonText: "Register",
            }));

            // Start the timer for OTP resend
            const interval = setInterval(() => {
                setAuthFormFields((prev) => ({
                    ...prev,
                    resendOtp: {
                        ...prev.resendOtp,
                        timer: prev.resendOtp.timer - 1,
                    },
                }));
            }, 1000);

            setTimeout(() => {
                clearInterval(interval);
                setAuthFormFields((prev) => ({
                    ...prev,
                    resendOtp: {
                        ...prev.resendOtp,
                        showButton: true,
                        timer: 0,
                    },
                }));
            }, 15000);
        })
        .catch((error) => {
            toast.error(error.response.data.message.general[0]);
        })
        .finally(() => {
            setAuthFormFields((prev) => ({
                ...prev,
                isLoading: false,
            }));
        });
};

export const registerUser = async ({
    email,
    name,
    otp,
    setAuthFormFields,
}: {
    email: string;
    name: string;
    otp: string;
    setAuthFormFields: Dispatch<React.SetStateAction<AuthFormFields>>;
}) => {
    setAuthFormFields((prev) => ({
        ...prev,
        isLoading: true,
    }));
    publicGateway
        .post(buildVerse.register, {
            email: email,
            otp: otp,
            name: name,
        })
        .then((response) => {
            toast.success(response.data.message.general[0] || "Registered successfully");
            localStorage.setItem("accessToken", response.data.response.access_token);
            localStorage.setItem("refreshToken", response.data.response.refresh_token);
            localStorage.setItem("userEmail", email);
            setAuthFormFields((prev) => ({
                ...prev,
                authenticated: true,
            }));
            privateGateway.post(pollUrls.onboardUser);
        })
        .catch((error) => {
            const errorMessage = error.response.data.message;

            setAuthFormFields((prev) => ({
                ...prev,
                email: {
                    error: errorMessage.email || undefined,
                    showField: true,
                },
                name: {
                    error: errorMessage.name || undefined,
                    showField: true,
                },
                otp: {
                    error: errorMessage.otp || undefined,
                    showField: true,
                },
                generalMessage: errorMessage.general[0],
                primaryButtonText: "Register",
            }));
        })
        .finally(() => {
            setAuthFormFields((prev) => ({
                ...prev,
                isLoading: false,
            }));
        });
};

export const loginUsingGoogle = async (
    credentialResponse: Omit<TokenResponse, "error" | "error_description" | "error_uri">,
    setAuthFormFields: Dispatch<React.SetStateAction<AuthFormFields>>
) => {
    try {
        const { access_token } = credentialResponse;

        if (!access_token) {
            console.error("No Google access token received");
            return;
        }

        // Send the token to FastAPI backend for verification
        const response = await publicGateway.post(buildVerse.googleLogin, {
            token: access_token,
        });

        const {
            access_token: newAccessToken,
            refresh_token,
            email,
            profile_pic_url,
        } = response.data.response;

        localStorage.setItem("accessToken", newAccessToken);
        localStorage.setItem("refreshToken", refresh_token);
        localStorage.setItem("userEmail", email);

        if (profile_pic_url) {
            localStorage.setItem("userImage", profile_pic_url);
        }

        setAuthFormFields((prev) => ({
            ...prev,
            authenticated: true,
        }));
        privateGateway.post(pollUrls.onboardUser);
    } catch (error) {
        console.error("Google login error:", error);
        toast.error("Google login Failed");
    }
};

export const generateOTP = async ({
    email,
    type,
    setAuthFormFields,
    setShowChangePasswordModal,
}: {
    email: string;
    type: string;
    setAuthFormFields?: Dispatch<React.SetStateAction<AuthFormFields>>;
    setShowChangePasswordModal?: Dispatch<React.SetStateAction<boolean>>;
}) => {
    setAuthFormFields &&
        setAuthFormFields((prev) => ({
            ...prev,
            isLoading: true,
        }));
    publicGateway
        .post(buildVerse.generateOTP, {
            email_or_phone: email,
            type: type,
        })
        .then((response) => {
            toast.success(response.data.message.general[0]);
            setShowChangePasswordModal && setShowChangePasswordModal(true);
            setAuthFormFields &&
                setAuthFormFields((prev) => ({
                    ...prev,
                    otp: {
                        error: [],
                        showField: true,
                    },
                    api: {
                        name: type === "Forget Password" ? "ResetPassword" : "Login",
                        type: null,
                    },
                    resendOtp: {
                        apiName: "GenerateOTP",
                        apiType: type,
                        showButton: true,
                        timer: 15,
                    },
                    generalMessage: response.data.message.general[0],
                    primaryButtonText: type === "Forget Password" ? "Reset Password" : "Verify OTP",
                }));

            // Start the timer for OTP resend
            const interval = setInterval(() => {
                setAuthFormFields &&
                    setAuthFormFields((prev) => ({
                        ...prev,
                        resendOtp: {
                            ...prev.resendOtp,
                            timer: prev.resendOtp.timer - 1,
                        },
                    }));
            }, 1000);

            setTimeout(() => {
                clearInterval(interval);
                setAuthFormFields &&
                    setAuthFormFields((prev) => ({
                        ...prev,
                        resendOtp: {
                            ...prev.resendOtp,
                            showButton: true,
                            timer: 0,
                        },
                    }));
            }, 15000);
        })
        .catch((error) => {
            if (error.response.data.statusCode === 1001 && setAuthFormFields) {
                setAuthFormFields((prev) => ({
                    ...prev,
                    password: {
                        error: [],
                        showField: false,
                    },
                }));
                preRegister({ email, setAuthFormFields });
            }
        })
        .finally(() => {
            setAuthFormFields &&
                setAuthFormFields((prev) => ({
                    ...prev,
                    isLoading: false,
                }));
        });
};

export const resetUserPassword = async ({
    email,
    otp,
    password,
    setAuthFormFields,
    navigate,
    setShowChangePasswordModal,
}: {
    email: string;
    otp: string;
    password: string;
    setAuthFormFields?: Dispatch<React.SetStateAction<AuthFormFields>>;
    navigate?: NavigateFunction;
    setShowChangePasswordModal?: Dispatch<React.SetStateAction<boolean>>;
    ruri?: string | null;
}) => {
    setAuthFormFields &&
        setAuthFormFields((prev) => ({
            ...prev,
            isLoading: true,
        }));
    publicGateway
        .post(buildVerse.resetPassword, {
            email: email,
            otp: otp,
            password: password,
        })
        .then((response) => {
            if (response.data.response.access_token) {
                localStorage.setItem("userEmail", email);
                localStorage.setItem("accessToken", response.data.response.access_token);
                localStorage.setItem("refreshToken", response.data.response.refresh_token);
                localStorage.setItem("userImage", response.data.response.profile_pic_url);

                toast.success("Password reset successfully");

                setShowChangePasswordModal && setShowChangePasswordModal(false);
                setAuthFormFields &&
                    setAuthFormFields((prev) => ({
                        ...prev,
                        authenticated: true,
                    }));

                navigate && navigate("/events");
            }
        })
        .catch((error) => {
            const errorMessage = error.response.data.message;
            setAuthFormFields &&
                setAuthFormFields((prev) => ({
                    ...prev,
                    email: {
                        error: errorMessage.email || undefined,
                        showField: true,
                    },
                    otp: {
                        error: errorMessage.otp || undefined,
                        showField: true,
                    },
                    password: {
                        error: errorMessage.password || undefined,
                        showField: true,
                    },
                    generalMessage: errorMessage.general[0],
                    primaryButtonText: "Reset Password",
                }));
        })
        .finally(() => {
            setAuthFormFields &&
                setAuthFormFields((prev) => ({
                    ...prev,
                    isLoading: false,
                }));
        });
};
