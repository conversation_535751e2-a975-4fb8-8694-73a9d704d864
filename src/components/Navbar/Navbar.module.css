.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    width: 100%;
}

.navImage {
    width: 100%;
    max-width: 10rem;
    height: 100%;
    object-fit: cover;
    border-radius: 0.5rem;
}

.goBackButton {
    background-color: #333132;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s;
    border: none;
}

.statusPill {
    padding: 0.1rem 0.3rem;
    border-radius: 0.2rem;
    background-color: #f0f0f0;
    color: #333;
    font-weight: bold;
    text-align: center;
    font-size: 0.8rem;
}

.row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 0.5rem;
}

.userEmail {
    font-size: 0.8rem;
    color: #333132;
}

.ideathonImage {
    max-width: 8rem;
    width: 100%;
    border-radius: 8px;
}

.mmpImage {
    max-width: 6rem;
    width: 100%;
    border-radius: 8px;
}

.logoutIcon {
    display: none;
}

@media (max-width: 468px) {
    .logoutButton {
        display: none;
    }

    .goBackButton {
        display: none;
    }

    .logoutIcon {
        display: block;
        cursor: pointer;
    }
}
