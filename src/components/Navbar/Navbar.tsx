import { useNavigate } from "react-router-dom";
import styles from "./Navbar.module.css";
import { FiRefreshCcw } from "react-icons/fi";
import { IoLogOut } from "react-icons/io5";
const Navbar = ({
    setConnectSocket,
    showMMP,
    showLogout = true,
    showName = true,
}: {
    setConnectSocket?: React.Dispatch<React.SetStateAction<boolean>>;
    showMMP?: boolean;
    showLogout?: boolean;
    showName?: boolean;
}) => {
    const navigate = useNavigate();
    const accessToken = localStorage.getItem("accessToken");
    const userEmail = localStorage.getItem("userEmail");
    return (
        <nav className={styles.navbar}>
            <div className={styles.row}>
                <img
                    src="\assets\navbar\navlogo.png"
                    onClick={() => {
                        navigate("/");
                    }}
                    alt=""
                    className={styles.navImage}
                />
            </div>

            <div className={styles.row}>
                {setConnectSocket && (
                    <FiRefreshCcw
                        style={{
                            marginLeft: "5px",
                        }}
                        className={styles.refreshIcon}
                        onClick={() => setConnectSocket((prev) => !prev)}
                    />
                )}
            </div>

            {showMMP && (
                <img
                    src="\navbar\mmpblack.png"
                    className={styles.mmpImage}
                    alt=""
                    onClick={() => (window.location.href = "https://makemypass.com")}
                />
            )}

            {(showName || showLogout) && (
                <div className={styles.row}>
                    {showName && userEmail && (
                        <div className={styles.row}>
                            <p className={styles.userEmail}>Hi, {userEmail?.split("@")[0]}</p>
                        </div>
                    )}

                    {accessToken && showLogout && (
                        <button
                            onClick={() => {
                                localStorage.clear();
                                navigate("/login");
                            }}
                            className={`${styles.goBackButton} ${styles.logoutButton}`}
                        >
                            Logout
                        </button>
                    )}

                    {accessToken && showLogout && (
                        <IoLogOut
                            className={styles.logoutIcon}
                            onClick={() => {
                                localStorage.clear();
                                navigate("/login");
                            }}
                            color="#333132"
                            size={25}
                        />
                    )}
                </div>
            )}
        </nav>
    );
};

export default Navbar;
