.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.modalContent {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    max-width: 500px;
    width: 100%;
    background-color: #202020;
    border-radius: 10px;

    padding-top: 0;
    color: white;

    border: 2px solid #2f2f2f;

    margin: 1rem;
}

.modalBody {
    padding: 2rem;
    padding-top: 1rem;
    width: 100%;
}

.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.5rem 2rem;
    background-color: #252525;
}

.modalTitle {
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.closeButton {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}
