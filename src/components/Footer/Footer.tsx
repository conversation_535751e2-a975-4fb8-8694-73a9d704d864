import { Dispatch, SetStateAction } from "react";
import styles from "./Footer.module.css";
import { FiRefreshCcw } from "react-icons/fi";

const Footer = ({
    socketStatus,
    setConnectSocket,
}: {
    socketStatus?: string;
    setConnectSocket?: Dispatch<SetStateAction<boolean>>;
}) => {
    return (
        <div className={styles.backgroundContainer}>
            <footer className={styles.footer}>
                <img
                    src="\assets\footer\footerlogo.png"
                    alt="Footer Logo"
                    className={styles.footerLogo}
                />
                <div className={styles.row}>
                    {socketStatus && (
                        <div className={styles.statusPill}>
                            <span
                                className={`${styles.statusCircle} ${
                                    socketStatus === "Connected"
                                        ? styles.connected
                                        : socketStatus === "Disconnected"
                                        ? styles.disconnected
                                        : styles.reconnecting
                                }`}
                            ></span>
                            {socketStatus}
                        </div>
                    )}
                    {setConnectSocket && (
                        <FiRefreshCcw
                            className={styles.refreshIcon}
                            onClick={() => setConnectSocket((prev) => !prev)}
                        />
                    )}
                </div>
            </footer>
        </div>
    );
};

export default Footer;
