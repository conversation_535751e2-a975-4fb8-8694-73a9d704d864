.backgroundContainer {
    background-color: #333132;
    color: #fbfbf5;
}

.footer {
    max-width: 1300px;
    margin: 0 auto;
    padding: 0.75rem 1rem;
    text-align: center;

    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.footerLogo {
    width: 100px;
    height: auto;
}

.socialIcons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.icon {
    color: #fbfbf5;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.icon:hover {
    color: #eaeae2; /* Example hover color */
}

.statusPill {
    background-color: #fbfbf5;
    color: #333132;
    padding: 0.1rem 0.5rem;
    border-radius: 9999px;
    display: inline-flex;
    align-items: center;
    gap: 0.2rem;
    font-weight: bold;
    font-size: 0.75rem;
}

.statusCircle {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
}

.connected {
    background-color: #4caf50; /* Green for connected */
}

.disconnected {
    background-color: #f44336; /* Red for disconnected */
}

.reconnecting {
    background-color: #ff9800; /* Orange for reconnecting */
}

.refreshIcon {
    cursor: pointer;
}

.row {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 0.5rem;
}
