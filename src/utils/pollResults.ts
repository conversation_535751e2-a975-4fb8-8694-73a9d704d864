import { PollResult } from "../types/poll";

export const calculateTotalScore = (result: PollResult): number => {
    return Object.entries(result).reduce((total, [key, value]) => {
        if (key !== "feedback" && typeof value === "number") {
            return total + value;
        }
        return total;
    }, 0);
};

export const hasScores = (result: PollResult): boolean => {
    return Object.entries(result).some(
        ([key, value]) => key !== "feedback" && typeof value === "number"
    );
};
