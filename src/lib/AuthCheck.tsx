import { Navigate, Outlet } from "react-router-dom";

const AuthCheck: React.FC = () => {
    const refreshToken = localStorage.getItem("refreshToken");
    const redirection = window.location.pathname.slice(1);

    if (!refreshToken) {
        if (redirection.endsWith("/voting")) {
            sessionStorage.setItem("judgeRedirect", "/" + redirection);
            return <Navigate to={`/login?userType=judge&ruri=/${redirection}`} />;
        }
        return <Navigate to={`/login?ruri=${redirection}`} />;
    }

    return <Outlet />;
};

export default AuthCheck;
