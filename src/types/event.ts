export interface Event {
    id: string;
    name: string;
    team_count?: number;
    public_voting?: boolean;
    resubmission_allowed?: boolean;
    is_admin_controlled?: boolean;
    presentation_time_limit?: number;
    voting_time_limit?: number;
    questions?: Question[];
    created_at?: string;
    updated_at?: string;
}

export interface Question {
    id: string;
    title: string;
    description: string;
    type: "number";
    max_mark: number;
}

export interface CreateEventRequest {
    name: string;
}

export interface UpdateEventRequest {
    name?: string;
    public_voting?: boolean;
    resubmission_allowed?: boolean;
    is_admin_controlled?: boolean;
    presentation_time_limit?: number;
    voting_time_limit?: number;
    questions?: Question[];
}

export interface EventApiResponse {
    hasError: boolean;
    statusCode: number;
    message: {
        general: string[];
    };
    response: Event;
}

export interface EventListApiResponse {
    hasError: boolean;
    statusCode: number;
    message: {
        general: string[];
    };
    response: Event[];
}
