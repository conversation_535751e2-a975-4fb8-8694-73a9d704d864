export interface Criteria {
    id: string;
    title: string;
    description: string;
    type: "number";
    max_mark: number;
}

export interface VotingScore {
    [criteriaId: string]: number;
}

export interface TeamInfo {
    id: string;
    status: string;
    team_code: string;
    team_name: string;
    [key: string]: string;
}

export interface VotingSubmission {
    teamId: string;
    scores: VotingScore;
    feedback: string;
}

export interface VerificationError {
    name?: string;
    email?: string;
    general?: string;
}
