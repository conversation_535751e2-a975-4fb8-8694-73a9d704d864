import { getEventId } from "../../apis/user";
import { pollStatusWS } from "../../../services/urls";
import { TeamInfo } from "../../types/voting";
import { NavigateFunction } from "react-router-dom";
import { Dispatch, SetStateAction } from "react";

export function useFetchEventId(eventName: string | undefined, setEventId: Dispatch<SetStateAction<string | undefined>>, setEventNotFound: Dispatch<SetStateAction<boolean>>) {
    if (eventName) getEventId(eventName, setEventId, setEventNotFound);
}

export function useAuthCheck(eventName: string | undefined, navigate: NavigateFunction) {
    const validationToken = localStorage.getItem("accessToken");
    if (validationToken === null) {
        navigate(`/login?userType=judge&ruri=${eventName}/voting`);
    } else {
        sessionStorage.removeItem("judgeRedirect");
    }
}

export function useVotingWebSocket({
    eventId,
    setIsLoading,
    setSocketStatus,
    setTeamInfo,
    setAlreadySubmitted,
    setCountdown,
    firstMessageRef,
}: {
    eventId: string | undefined;
    setIsLoading: (loading: boolean) => void;
    setSocketStatus: (status: string) => void;
    setTeamInfo: (info: TeamInfo) => void;
    setAlreadySubmitted: (submitted: boolean) => void;
    setCountdown: (count: number) => void;
    firstMessageRef: React.MutableRefObject<boolean>;
    setConnectSocket: (connect: boolean) => void;
}) {
    const accessToken = localStorage.getItem("accessToken");
    if (eventId && accessToken && accessToken.length > 0) {
        setIsLoading(true);
        const wsURL = `${import.meta.env.VITE_WS_URL}${pollStatusWS(eventId)}?Authorization=Bearer ${accessToken}`;
        let ws: WebSocket | null = null;
        const connectWebSocket = () => {
            if (ws) ws.close();
            ws = new WebSocket(wsURL);
            ws.onopen = () => {
                setSocketStatus("Connected");
                firstMessageRef.current = true;
            };
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (
                    data.response.status === "Voting" &&
                    !firstMessageRef.current &&
                    !data.response.vote_submitted
                ) {
                    localStorage.removeItem("userVotingStartingTime");
                    setCountdown(3);
                }
                setAlreadySubmitted(data.vote_submitted);
                setTeamInfo(data.response);
                if (data.message !== "Connection Ok") {
                    firstMessageRef.current = false;
                }
            };
            ws.onerror = (error) => {
                setSocketStatus("Disconnected");
                console.error("WebSocket error:", error);
            };
            ws.onclose = () => {
                setSocketStatus("Reconnecting...");
                setTimeout(connectWebSocket, 6000);
            };
        };
        connectWebSocket();
        setIsLoading(false);
        return () => {
            if (ws) ws.close();
        };
    }
    return undefined;
}

export function useCountdown(countdown: number, setCountdown: (count: number) => void) {
    if (countdown > 0) {
        const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
        return () => clearTimeout(timer);
    }
    return undefined;
}
