.noTeamLiveContainer {
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 65vh;
    border-radius: 8px;
    margin: 20px 0;

    max-width: 35rem;
}

.noTeamLiveContainer h2 {
    font-size: 24px;
    color: #343a40;
    margin-bottom: 10px;
}

.subText {
    font-size: 16px;
    color: #6c757d;
}

.backgroundContainer {
    background-color: #fbfbf5;
    min-height: calc(100vh - 3.3rem);
}

.container {
    max-width: 1300px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.countdown {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    height: 100%;
    min-height: 60vh;
    color: #fff;
}

.countdownText {
    font-size: 4rem;
    font-weight: 600;
}

.votingContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #333;
}

.loaderContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 65vh;
}

.form {
    width: 100%;
    margin: 20px 0;
    border-radius: 8px;
    color: #fff;
}

.inputGroup {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    color: #fff;
}

.label {
    margin-bottom: 5px;
    font-size: 14px; /* Reduced font size */
    color: #fff;
}

.input {
    padding: 8px; /* Reduced padding */
    font-size: 14px; /* Reduced font size */
    border: 1px solid #555;
    border-radius: 4px;
    background-color: #444;
    color: #fff;
}

.submitButton {
    padding: 8px 16px; /* Reduced padding */
    font-size: 14px; /* Reduced font size */
    color: #333;
    background-color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.inputHelperDescription {
    font-size: 12px;
    color: #fff;
    opacity: 0.7;
    margin-bottom: 0.5rem;
}

.submitButton:hover {
    background-color: #ccc;
}

.formNote {
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.error {
    color: rgb(244, 86, 86);
    font-size: 14px;
    margin-top: 5px;
}

.container {
    background-color: #fbfbf5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.content {
    max-width: 35rem;
    width: 100%;
    min-height: 76vh;
    height: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border-radius: 0.5rem;
}

.errorCode {
    margin-bottom: 1.5rem;
}

.errorCode span {
    font-size: 4rem;
    font-weight: 700;
    color: #111827;
}

.title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.description {
    color: #4b5563;
    margin-bottom: 2rem;
    max-width: 28rem;
}

@media (min-width: 640px) {
    .errorCode span {
        font-size: 6rem;
    }

    .title {
        font-size: 1.875rem;
    }
}

.logoutButton {
    background-color: #fff;
    color: #333;
    padding: 8px 16px; /* Reduced padding */
    border: 1px solid #333;
    border-radius: 4px; /* Reduced border radius */
    cursor: pointer;
    font-size: 14px; /* Reduced font size */
    margin-top: 20px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.logoutButton:hover {
    background-color: #333;
    color: #fff;
}
