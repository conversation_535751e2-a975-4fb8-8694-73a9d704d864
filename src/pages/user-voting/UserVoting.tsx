import { useState, useEffect, useRef } from "react";
import { TeamInfo } from "../../types/voting";
import VotingForm from "./components/ui/VotingForm/VotingForm";
import styles from "./UserVoting.module.css";
import Navbar from "../../components/Navbar/Navbar";
import Footer from "../../components/Footer/Footer";

import { motion } from "framer-motion";
import { HashLoader } from "react-spinners";

import { GoAlertFill } from "react-icons/go";
import { useNavigate, useParams } from "react-router-dom";

import {
    useFetchEventId,
    useAuthCheck,
    useVotingWebSocket,
    useCountdown
} from "./functions";
import { countdownVariants } from "./constants";

function UserVoting() {
    const [teamInfo, setTeamInfo] = useState<TeamInfo | null>(null);
    const [countdown, setCountdown] = useState<number>(0);

    const [eventId, setEventId] = useState<string>();
    const [socketStatus, setSocketStatus] = useState<string>("");
    const { eventName } = useParams<{ eventName: string }>();
    const [alreadySubmitted, setAlreadySubmitted] = useState<boolean>(false);

    const [eventNotFound, setEventNotFound] = useState<boolean>(false);
    const [connectSocket, setConnectSocket] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const navigate = useNavigate();

    useEffect(() => {
        useFetchEventId(eventName, setEventId, setEventNotFound);
    }, [eventName]);

    useEffect(() => {
        useAuthCheck(eventName, navigate);
    }, []);

    const firstMessageRef = useRef<boolean>(true);

    useEffect(() => {
        const cleanup = useVotingWebSocket({
            eventId,
            setIsLoading,
            setSocketStatus,
            setTeamInfo,
            setAlreadySubmitted,
            setCountdown,
            firstMessageRef,
            setConnectSocket,
        });
        return cleanup;
    }, [eventId, connectSocket]);

    useEffect(() => {
        return useCountdown(countdown, setCountdown);
    }, [countdown]);

    return (
        <>
            <div className={styles.backgroundContainer}>
                <div className={styles.container}>
                    <Navbar setConnectSocket={setConnectSocket} />
                    {!eventNotFound ? (
                        <>
                            {isLoading ? (
                                <>
                                    <div className={styles.loaderContainer}>
                                        <HashLoader size={50} />
                                    </div>
                                </>
                            ) : (
                                <>
                                    {countdown > 0 ? (
                                        <div className={styles.countdown}>
                                            <div className={styles.votingContainer}>
                                                <p className={styles.votingSubText}>
                                                    Voting starts in
                                                </p>
                                                <motion.p
                                                    className={styles.countdownText}
                                                    key={countdown}
                                                    variants={countdownVariants}
                                                    initial="initial"
                                                    animate="animate"
                                                    exit="exit"
                                                >
                                                    {countdown}
                                                </motion.p>
                                            </div>
                                        </div>
                                    ) : teamInfo && teamInfo.id && teamInfo.status !== "Idle" ? (
                                        <VotingForm
                                            team={teamInfo}
                                            alreadySubmitted={alreadySubmitted}
                                            setAlreadySubmitted={setAlreadySubmitted}
                                        />
                                    ) : (
                                        <div className={styles.noTeamLiveContainer}>
                                            <GoAlertFill size={35} />
                                            <>
                                                <h2>No team is currently presenting</h2>
                                                <p className={styles.subText}>
                                                    Currently, there are no teams voting or
                                                    presenting. Kindly wait for the voting process
                                                    to start.
                                                </p>
                                            </>
                                        </div>
                                    )}
                                </>
                            )}
                        </>
                    ) : (
                        <div className={styles.content}>
                            <div className={styles.errorCode}>
                                <span>404</span>
                            </div>

                            <h2 className={styles.title}>Page not found</h2>

                            <p className={styles.description}>
                                Sorry, we couldn't find the page you're looking for. Perhaps you've
                                mistyped the URL or the page has been moved.
                            </p>
                        </div>
                    )}
                </div>
            </div>
            <Footer socketStatus={socketStatus} setConnectSocket={setConnectSocket} />
        </>
    );
}

export default UserVoting;
