.teamHeaderContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.header {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid #eee;
    width: 100%;

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.teamCardLogo {
    width: 100%;
    max-width: 4rem;
    height: 100%;
    object-fit: cover;
    border-radius: 0.25rem;
}

.title {
    font-size: 1.5rem;
    font-weight: 500;
    color: #333132;
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 600px) {
    .title {
        font-size: 1.15rem;
    }
}

.meta {
    display: flex;
    gap: 1rem;
    color: #666;
    font-size: 0.875rem;
}

.label {
    font-weight: 500;
}

.statusPill {
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    background-color: #333132; /* Dark background */
    display: inline-block;
    text-align: center;
    margin-right: 0.5rem;
}

.alreadyVoted {
    background-color: #ffe6e6;
    color: #d32f2f;
}

@media (max-width: 600px) {
    .statusPill {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

.teamSummary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    text-align: left;
    font-size: 0.95rem;
    margin-top: 1rem;
}

.teamHeaderTopBar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 0.5rem;
    gap: 1rem;
    flex-wrap: wrap;
    position: relative;
}

.teamHeaderText {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5rem;
    width: 100%;
}

.timerContainer {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    position: absolute;
    top: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timerText,
.timer {
    color: #666;
    margin: 0;
}

.timerText {
    font-size: 0.8rem;
}

.timer {
    font-size: 0.8rem;
    font-weight: bold;
}

.timerRed {
    color: #d32f2f;
}

.sectorPill {
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
    background-color: #333132; /* Dark background */
    display: inline-block;
    text-align: center;
    margin-right: 0.5rem;
    opacity: 0.8;
}
