import { PiMicrophoneStageFill } from "react-icons/pi";
import { TeamInfo } from "../../../../../types/voting";
import styles from "./TeamHeader.module.css";
import { FaVoteYea } from "react-icons/fa";

interface TeamHeaderProps {
    team: TeamInfo;
    alreadySubmitted: boolean;
}

export default function TeamHeader({ team, alreadySubmitted }: TeamHeaderProps) {
    return (
        <div className={styles.header}>
            <div className={styles.teamHeaderTopBar}>
                {team.logo && typeof team.logo === "string" && (
                    <img src={team.logo} alt="" className={styles.teamCardLogo} />
                )}
                <div className={styles.teamHeaderText}>
                    <div className={styles.teamHeaderContainer}>
                        <h1 className={styles.title}>
                            {team.status === "Presenting" ? (
                                <PiMicrophoneStageFill className={styles.presentingIcon} />
                            ) : team.status === "Voting" ? (
                                <FaVoteYea />
                            ) : null}
                            {team.team_name}
                            <span className={styles.sectorPill}>{team.sector}</span>
                        </h1>
                        <p
                            className={`${styles.statusPill} ${
                                (team.vote_submitted && team.status === "Voting") ||
                                (alreadySubmitted && team.status === "Voting")
                                    ? styles.alreadyVoted
                                    : ""
                            }`}
                        >
                            {(team.vote_submitted && team.status === "Voting") ||
                            (alreadySubmitted && team.status === "Voting")
                                ? "Already Voted"
                                : team.status}
                        </p>
                    </div>
                    {team.team_code && (
                        <div className={styles.meta}>
                            <div>
                                <span className={styles.label}>Code: </span>
                                {team.team_code}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {team.summary && <p className={styles.teamSummary}>{team.summary}</p>}
        </div>
    );
}
