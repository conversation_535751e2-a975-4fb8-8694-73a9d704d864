export const customStyles = {
    container: (provided: any) => ({
        ...provided,
        margin: "1rem auto",
        width: "100%",
        minHeight: "65vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
    }),

    control: (provided: any, state: any) => ({
        ...provided,
        width: "100%",
        backgroundColor: "#333132",
        color: "white",
        border: "none",
        borderRadius: "0.5rem",
        padding: "1rem",
        fontSize: "1rem",
        fontWeight: 500,
        cursor: state.isDisabled ? "not-allowed" : "pointer",
        transition: "background 0.2s ease",
        "&:hover": {
            backgroundColor: state.isDisabled ? "#ccc" : "#494748",
        },
    }),

    option: (provided: any, state: any) => ({
        ...provided,
        padding: "0.75rem",
        backgroundColor: state.isSelected
            ? "#494748"
            : state.isFocused
            ? "#f5f5f5"
            : "transparent",
        color: state.isSelected ? "white" : "#333132",
        fontSize: "1rem",
        fontWeight: "500",
        cursor: "pointer",
    }),

    singleValue: (provided: any) => ({
        ...provided,
        color: "#ffffff",
        fontSize: "1rem",
        fontWeight: 500,
    }),

    placeholder: (provided: any) => ({
        ...provided,
        color: "#ffffff",
        fontSize: "1rem",
        fontWeight: 500,
    }),

    dropdownIndicator: (provided: any) => ({
        ...provided,
        color: "white",
        "&:hover": {
            color: "white",
        },
    }),

    indicatorSeparator: (provided: any) => ({
        ...provided,
        backgroundColor: "transparent",
    }),

    menu: (provided: any) => ({
        ...provided,
        backgroundColor: "#fff",
        borderRadius: "0.5rem",
        padding: "0.5rem 0",
    }),

    menuList: (provided: any) => ({
        ...provided,
        padding: 0,
    }),
};
