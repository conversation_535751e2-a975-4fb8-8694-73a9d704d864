import { Dispatch, SetStateAction, useEffect, useState } from "react";
import styles from "./VotingForm.module.css";
import { Criteria, TeamInfo, VotingScore } from "../../../../../types/voting";
import TeamHeader from "../TeamHeader/TeamHeader";
import CriteriaSlider from "../CriteriaSlider/CriteriaSlider";
import { getEventId, getQuestions } from "../../../../../apis/user";
import { BeatLoader } from "react-spinners";
import { useParams } from "react-router-dom";
import Modal from "../../../../../components/Modal/Modal";
import { handleSubmit, handleScoreChange } from "./functions";

interface VotingFormProps {
    team: TeamInfo;
    alreadySubmitted: boolean;
    setAlreadySubmitted: Dispatch<SetStateAction<boolean>>;
}

export default function VotingForm({
    team,
    alreadySubmitted,
    setAlreadySubmitted,
}: VotingFormProps) {
    const [criterias, setCriterias] = useState<Criteria[]>([]);
    const [scores, setScores] = useState<VotingScore>(() => {
        // Initialize scores with 0 for each criteria
        return criterias.reduce(
            (acc, criteria) => ({
                ...acc,
                [criteria.id]: 0,
            }),
            {}
        );
    });

    const { eventName } = useParams<{ eventName: string }>();

    const [eventId, setEventId] = useState<string>();
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const [showConfirmationModal, setShowConfirmationModal] = useState<boolean>(false);

    useEffect(() => {
        if (eventName) getEventId(eventName, setEventId);
    }, [eventName]);

    useEffect(() => {
        if (eventId) getQuestions(eventId, setCriterias);
    }, [eventId]);

    useEffect(() => {
        setScores((prev) => {
            const newScores = { ...prev };
            criterias.forEach((criteria) => {
                if (prev[criteria.id] === undefined) {
                    newScores[criteria.id] = 0;
                }
            });
            return newScores;
        });
    }, [criterias]);

    useEffect(() => {
        if (!team.vote_submitted) {
            const teamId = team.id;
            localStorage.setItem("teamId", teamId);
        }
    }, [team.id, setAlreadySubmitted]);

    const handleSubmitClick = () => {
        handleSubmit(team.id, scores, setIsLoading, setAlreadySubmitted, setShowConfirmationModal);
    };

    const handleScoreChangeClick = (criteriaId: string, value: number) => {
        handleScoreChange(criteriaId, value, setScores);
    };

    return (
        <>
            {showConfirmationModal && (
                <Modal title="Confirm Submission" onClose={() => setShowConfirmationModal(false)}>
                    <div className={styles.confirmationContent}>
                        <ul className={styles.scoresList}>
                            {criterias.map((criteria) => (
                                <li key={criteria.id} className={styles.scoreItem}>
                                    <p className={styles.criteriaTitle}>
                                        {criteria.title}:{" "}
                                        <span>
                                            {scores[criteria.id] ?? 0}/{criteria.max_mark}
                                        </span>
                                    </p>{" "}
                                </li>
                            ))}
                        </ul>

                        <button
                            onClick={() => {
                                handleSubmitClick();
                            }}
                            className={styles.confirmButton}
                            disabled={isLoading}
                        >
                            {isLoading ? <BeatLoader size={10} color="white" /> : "Confirm"}
                        </button>
                    </div>
                </Modal>
            )}

            <form
                onSubmit={(e) => {
                    e.preventDefault();
                    setShowConfirmationModal(true);
                }}
                className={styles.container}
            >
                {team.status === "Presenting" && (
                    <div className={styles.noTeamLiveContainer}>
                        <TeamHeader team={team} alreadySubmitted={alreadySubmitted} />
                        <>
                            <p className={styles.subText}>
                                Once the presentation ends, the voting begins.
                            </p>
                        </>
                    </div>
                )}
                {team.status === "Voting" && (
                    <>
                        <TeamHeader team={team} alreadySubmitted={alreadySubmitted} />
                        {!team.vote_submitted && !alreadySubmitted ? (
                            <>
                                {criterias.map((criteria) => (
                                    <CriteriaSlider
                                        key={criteria.id}
                                        criteria={criteria}
                                        value={scores[criteria.id]}
                                        onChange={(value) => handleScoreChangeClick(criteria.id, value)}
                                    />
                                ))}

                                <button
                                    type="submit"
                                    className={styles.submitButton}
                                    disabled={isLoading || alreadySubmitted}
                                >
                                    {isLoading ? (
                                        <BeatLoader size={10} color="white" />
                                    ) : alreadySubmitted ? (
                                        "Already Submitted"
                                    ) : (
                                        "Submit"
                                    )}
                                </button>
                            </>
                        ) : (
                            <>
                                <p className={styles.subText}>
                                    Your vote has been submitted. Thank you for voting!
                                </p>
                            </>
                        )}
                    </>
                )}
            </form>
        </>
    );
}
