.container {
    max-width: 800px;
    margin: 1rem auto;
    width: 100%;
    min-height: 65vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.submitButton {
    width: 100%;
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.submitButton:hover {
    background: #494748;
}

.submitButton:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.noTeamLiveContainer {
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 65vh;
    border-radius: 8px;
    margin: 20px 0;
    max-width: 35rem;
}

.noTeamLiveContainer h2 {
    font-size: 24px;
    color: #343a40;
    margin-bottom: 10px;
}

.subText {
    font-size: 14px;
    text-align: center;
}

.confirmationContent {
    text-align: left;
}

.confirmationTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.scoresList {
    padding: 0;
    padding-left: 1rem;
    margin: 0 0 1rem 0;
}

.scoreItem {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.feedbackSection {
    margin-top: 1rem;
    margin-bottom: 1rem;
    text-align: left;
    opacity: 0.8;
}

.feedbackSection strong {
    display: block;
    margin-bottom: 0.5rem;
}

.criteriaTitle {
    opacity: 0.8;

    span {
        opacity: 1;
        color: white;
    }
}

.feedbackSection p {
    font-size: 0.875rem;
}

.confirmButton {
    width: 100%;
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.confirmButton:hover {
    background: #494748;
}

.confirmButton:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.investingScaleContainer {
    text-align: left;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    width: 100%;
}

.investingScaleTitle {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.investingScaleSelect {
    width: 100%;
}

.timerContainer {
    position: fixed;
    bottom: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    z-index: 1000;
}

.timer {
    margin: 0;
}

.timerRed {
    color: red;
}


.phoneInputContainer {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
    width: 100%;
}

.phoneLabel {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.phoneInput {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 0.25rem;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.phoneInput:focus {
    border-color: #333132;
    outline: none;
}