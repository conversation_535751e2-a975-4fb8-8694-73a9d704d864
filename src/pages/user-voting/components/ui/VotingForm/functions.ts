import { submitPoll } from "../../../../../apis/user";
import { VotingScore } from "../../../../../types/voting";
import { Dispatch, SetStateAction } from "react";

export function handleSubmit(
    teamId: string,
    scores: VotingScore,
    setIsLoading: Dispatch<SetStateAction<boolean>>,
    setAlreadySubmitted: Dispatch<SetStateAction<boolean>>,
    setShowConfirmationModal: Dispatch<SetStateAction<boolean>>
) {
    submitPoll(
        teamId,
        {
            team_id: teamId,
            scores,
        },
        setIsLoading,
        setAlreadySubmitted,
        setShowConfirmationModal
    );
}

export function handleScoreChange(
    criteriaId: string,
    value: number,
    setScores: Dispatch<SetStateAction<VotingScore>>
) {
    setScores((prev) => ({
        ...prev,
        [criteriaId]: value,
    }));
} 