.criteria {
    background: white;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 0.5rem;
    border: 1px solid #eee;
    width: 100%;
}

.header {
    margin-bottom: 1rem;
}

.title {
    font-size: 1rem;
    font-weight: 500;
    color: #333132;
    margin-bottom: 0.25rem;
}

.maxMark {
    font-size: 0.65rem;
    color: #666;
    margin-left: 0.25rem;
    background: #f0f0f0;
    padding: 0.25rem 0.5rem;
    border-radius: 999px;
}

@media (max-width: 600px) {
    .title {
        font-size: 0.9rem;
    }
}

.description {
    font-size: 0.85rem;
    color: #666;
}

.sliderContainer {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.slider {
    flex: 1;
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #333132;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    background: #494748;
}

.value {
    font-size: 1rem;
    font-weight: 500;
    color: #333132;
    min-width: 2rem;
    text-align: center;
}
