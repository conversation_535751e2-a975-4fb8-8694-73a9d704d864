import { Criteria } from "../../../../../types/voting";
import styles from "./CriteriaSlider.module.css";

interface CriteriaSliderProps {
    criteria: Criteria;
    value: number;
    onChange: (value: number) => void;
}

export default function CriteriaSlider({ criteria, value, onChange }: CriteriaSliderProps) {
    return (
        <div className={styles.criteria}>
            <div className={styles.header}>
                <h3 className={styles.title}>
                    {criteria.title}
                    <span className={styles.maxMark}>Total: {criteria.max_mark}</span>
                </h3>
                <p className={styles.description}>{criteria.description}</p>
            </div>
            <div className={styles.sliderContainer}>
                <input
                    type="range"
                    min="0"
                    max={criteria.max_mark}
                    step="0.5"
                    value={value}
                    onChange={(e) => onChange(Number(e.target.value))}
                    className={styles.slider}
                />
                <span className={styles.value}>{value}</span>
            </div>
        </div>
    );
}
