import styles from "./FeedbackField.module.css";

interface FeedbackFieldProps {
    value: string;
    onChange: (value: string) => void;
}

export default function FeedbackField({ value, onChange }: FeedbackFieldProps) {
    return (
        <div className={styles.feedback}>
            <label htmlFor="feedback" className={styles.label}>
                Additional Feedback
            </label>
            <textarea
                id="feedback"
                value={value}
                onChange={(e) => onChange(e.target.value)}
                placeholder="Share your thoughts about this team's idea and presentation..."
                className={styles.textarea}
            />
        </div>
    );
}
