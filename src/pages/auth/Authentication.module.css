.backgroundContainer {
    background-color: #fbfbf5;
    min-height: calc(100vh - 3.3rem);

    padding: 1rem;
}

.container {
    max-width: 1300px;
    margin: 0 auto;
    height: 100%;
}

.formContainer {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
}

.form {
    background-color: #333132;
    border-radius: 0.5rem;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.formFields {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.inputField {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 0.25rem;
    font-size: 0.875rem; /* Reduced font size */
    transition: border-color 0.2s ease;
}

.inputField:focus {
    outline: none;
    border-color: #f5f5f5;
}

.passwordResetContainer {
    margin-top: -0.5rem;
    margin-bottom: 1rem;
}

.passwordReset {
    font-size: 0.75rem; /* Reduced font size */
    color: #f5f5f5;
    text-align: right;
}

.passwordReset span {
    cursor: pointer;
    text-decoration: underline;
}

.alertMessage {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ff6384;
    font-size: 0.75rem; /* Reduced font size */
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: rgba(255, 99, 132, 0.1);
    border-radius: 0.25rem;
}

.formFooter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.formFooter p {
    font-size: 0.75rem; /* Reduced font size */
    color: #f5f5f5;
}

.submitButton {
    background-color: #f5f5f5;
    color: #333132;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem; /* Reduced font size */
    font-weight: 500;
    cursor: pointer;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    transition: background-color 0.2s ease;
}

.submitButton:hover {
    background-color: #fbfbf5;
}

.submitButton:disabled {
    background-color: #666;
    cursor: not-allowed;
}

.orContainer {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1.5rem 0;
}

.line {
    flex: 1;
    height: 1px;
    background-color: #f5f5f5;
}

.or {
    color: #ccc;
    font-size: 0.75rem; /* Reduced font size */
}

.formAltLoginContainer {
    display: flex;
    justify-content: center;
}

.googleIcon {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    border: 1px solid #f5f5f5;
    border-radius: 0.5rem;
    font-size: 0.875rem; /* Reduced font size */
    color: #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.googleIcon:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.formHeaderTexts {
    font-size: 1rem; /* Increased font size */
    font-weight: 600;
    color: #f5f5f5;
    margin-bottom: 0.5rem;
}

.formHeaderDescription {
    font-size: 0.9rem; /* Standard font size */
    color: #f5f5f5;
    margin-bottom: 1.5rem;
}

.error {
    color: rgb(252, 83, 83);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}
