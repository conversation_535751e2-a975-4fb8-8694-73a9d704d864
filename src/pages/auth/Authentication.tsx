import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { FaGoogle } from "react-icons/fa6";
import { TbAlertTriangleFilled } from "react-icons/tb";
import { useNavigate } from "react-router";
import { BeatLoader } from "react-spinners";

import { generateOTP } from "../../apis/auth";
import styles from "./Authentication.module.css";
import { AuthFormFields } from "./types";
import Navbar from "../../components/Navbar/Navbar";
import Footer from "../../components/Footer/Footer";
import { handleSubmitLogic, handleResendOtpLogic } from "./functions";
import { useGoogleLogin } from "@react-oauth/google";
import { loginUsingGoogle } from "../../apis/auth";

const Authentication = () => {
    const navigate = useNavigate();
    const ruri = new URLSearchParams(window.location.search).get("ruri");

    const emailRef = useRef<HTMLInputElement>(null);
    const nameRef = useRef<HTMLInputElement>(null);
    const otpRef = useRef<HTMLInputElement>(null);
    const passwordRef = useRef<HTMLInputElement>(null);

    const [authFormFields, setAuthFormFields] = useState<AuthFormFields>({
        email: {
            error: [],
            showField: true,
        },
        name: {
            error: [],
            showField: false,
        },
        otp: {
            error: [],
            showField: false,
        },
        password: {
            error: [],
            showField: false,
        },
        api: {
            name: "GenerateOTP",
            type: "Login",
        },
        resendOtp: {
            apiName: "GenerateOTP",
            apiType: "Login",
            showButton: false,
            timer: 0,
        },
        primaryButtonText: "Get OTP",
        authenticated: false,
        loginType: "password",
        isLoading: false,
    });

    useEffect(() => {
        if (localStorage.getItem("accessToken")) {
            setAuthFormFields((prev) => ({
                ...prev,
                authenticated: true,
            }));
        }

        console.log(authFormFields);

        if (authFormFields.authenticated) {
            const judgeRedirect = sessionStorage.getItem("judgeRedirect");
            if (judgeRedirect) navigate(judgeRedirect);
            else navigate("/events");
        }
    }, [authFormFields.authenticated, navigate, ruri]);

    const handleSubmit = () => {
        handleSubmitLogic({
            authFormFields,
            setAuthFormFields,
            emailRef,
            nameRef,
            otpRef,
            passwordRef,
            navigate,
        });
    };

    const handleResendOtp = () => {
        handleResendOtpLogic({
            authFormFields,
            setAuthFormFields,
            emailRef,
        });
    };

    // google auth
    const handleGoogleLogin = useGoogleLogin({
        onSuccess: (credentialResponse) => {
            loginUsingGoogle(credentialResponse, setAuthFormFields);
        },
        onError: () => {
            console.error("Google Sign-In was unsuccessful");
        },
    });
    
    return (
        <>
            <div className={styles.backgroundContainer}>
                <Navbar />
                <div className={styles.container}>
                    <div className={styles.formContainer}>
                        <motion.div
                            initial={{
                                opacity: 0,
                                y: 100,
                            }}
                            animate={{
                                opacity: 1,
                                y: 0,
                            }}
                            transition={{
                                duration: 0.5,
                            }}
                            className={styles.form}
                        >
                            <p className={styles.formHeaderTexts}>
                                {authFormFields.api.name === "Login" && "Welcome Back to JusVote"}
                                {authFormFields.api.name === "Register" && "Join JusVote Today"}
                                {authFormFields.api.name === "GenerateOTP" && "Verify Your Email"}
                                {authFormFields.api.name === "ResetPassword" &&
                                    "Reset Your Password"}
                                {authFormFields.api.name === "PreRegister" &&
                                    "Get Started with JusVote"}
                            </p>
                            <p className={styles.formHeaderDescription}>
                                {authFormFields.api.name === "Login" &&
                                    "Sign In now to make your event Awesome!"}
                                {authFormFields.api.name === "Register" &&
                                    "Sign Up now to make your event Awesome!"}
                                {authFormFields.api.name === "GenerateOTP" &&
                                    "Enter your email to receive an OTP."}
                                {authFormFields.api.name === "ResetPassword" &&
                                    "Enter your email to reset your password."}
                                {authFormFields.api.name === "PreRegister" &&
                                    "Enter your email to get started."}
                            </p>
                            <div className={styles.formFields}>
                                {authFormFields.email.showField && (
                                    <input
                                        ref={emailRef}
                                        type="email"
                                        name="email"
                                        id="email"
                                        placeholder="Enter your Email*"
                                        className={styles.inputField}
                                    />
                                )}

                                {authFormFields.name.showField && (
                                    <>
                                        <input
                                            ref={nameRef}
                                            type="text"
                                            name="name"
                                            id="name"
                                            placeholder="Enter Your Name"
                                            className={styles.inputField}
                                        />
                                        {authFormFields.name.error?.length > 0 && (
                                            <p className={styles.error}>
                                                {authFormFields.name.error[0]}
                                            </p>
                                        )}
                                    </>
                                )}

                                {authFormFields.otp.showField && (
                                    <>
                                        <input
                                            ref={otpRef}
                                            type="number"
                                            name="otp"
                                            id="otp"
                                            placeholder="Enter OTP*"
                                            className={styles.inputField}
                                        />
                                        {authFormFields.otp.error?.length > 0 && (
                                            <p className={styles.error}>
                                                {authFormFields.otp.error[0]}
                                            </p>
                                        )}
                                    </>
                                )}

                                <>
                                    {authFormFields.password.showField && (
                                        <>
                                            <input
                                                ref={passwordRef}
                                                type="password"
                                                name="password"
                                                id="password"
                                                placeholder="Enter Password*"
                                                className={styles.inputField}
                                            />
                                            {authFormFields.password.error?.length > 0 && (
                                                <p className={styles.error}>
                                                    {authFormFields.password.error[0]}
                                                </p>
                                            )}
                                        </>
                                    )}

                                    {authFormFields.api.name !== "ResetPassword" &&
                                        authFormFields.loginType === "otp" && (
                                            <div className={styles.passwordResetContainer}>
                                                <p className={styles.passwordReset}>
                                                    <span
                                                        className="pointer"
                                                        onClick={() => {
                                                            if (
                                                                authFormFields.email.showField &&
                                                                emailRef.current?.value === ""
                                                            ) {
                                                                setAuthFormFields((prev) => ({
                                                                    ...prev,
                                                                    email: {
                                                                        error: [
                                                                            "Email is required",
                                                                        ],
                                                                        showField: true,
                                                                    },
                                                                }));
                                                                return;
                                                            }
                                                            setAuthFormFields((prev) => ({
                                                                ...prev,
                                                                email: {
                                                                    error: [],
                                                                    showField: true,
                                                                },
                                                                name: {
                                                                    error: [],
                                                                    showField: false,
                                                                },
                                                                otp: {
                                                                    error: [],
                                                                    showField: false,
                                                                },
                                                                password: {
                                                                    error: [],
                                                                    showField: true,
                                                                },
                                                                api: {
                                                                    name: "ResetPassword",
                                                                    type: null,
                                                                },
                                                                resendOtp: {
                                                                    apiName: "",
                                                                    apiType: null,
                                                                    showButton: true,
                                                                    timer: 0,
                                                                },
                                                                primaryButtonText: "Get OTP",
                                                            }));
                                                            generateOTP({
                                                                email: emailRef.current
                                                                    ?.value as string,
                                                                type: "Forget Password",
                                                                setAuthFormFields,
                                                            });
                                                        }}
                                                    >
                                                        Forgot Password?
                                                    </span>
                                                </p>
                                            </div>
                                        )}
                                </>
                            </div>

                            {authFormFields.generalMessage && (
                                <p className={styles.alertMessage}>
                                    <div
                                        style={{
                                            minWidth: "20px",
                                        }}
                                    >
                                        <TbAlertTriangleFilled size={20} color="#FF6384" />
                                    </div>
                                    {authFormFields.generalMessage}
                                </p>
                            )}
                            <div className={styles.formFooter}>
                                <p
                                    className="pointer"
                                    onClick={() => {
                                        setAuthFormFields((prev) => ({
                                            ...prev,
                                            email: {
                                                error: [],
                                                showField: true,
                                            },
                                            name: {
                                                error: [],
                                                showField: false,
                                            },
                                            otp: {
                                                error: [],
                                                showField: false,
                                            },
                                            password: {
                                                error: [],
                                                showField:
                                                    authFormFields.loginType === "password"
                                                        ? true
                                                        : false,
                                            },
                                            api: {
                                                name:
                                                    authFormFields.loginType === "password"
                                                        ? "Login"
                                                        : "GenerateOTP",
                                                type:
                                                    authFormFields.loginType === "otp"
                                                        ? "Login"
                                                        : null,
                                            },
                                            resendOtp: {
                                                apiName: "",
                                                apiType: "",
                                                showButton: false,
                                                timer: 0,
                                            },
                                            primaryButtonText:
                                                authFormFields.loginType === "otp"
                                                    ? "Get OTP"
                                                    : "Login",
                                            generalMessage: "",
                                            authenticated: false,
                                            loginType:
                                                authFormFields.loginType === "otp"
                                                    ? "password"
                                                    : "otp",
                                        }));
                                    }}
                                >
                                    Login with{" "}
                                    {authFormFields.loginType === "otp" ? "OTP" : "Password"}
                                </p>

                                {authFormFields.resendOtp.showButton && (
                                    <motion.button
                                        whileHover={{ scale: 1.05 }}
                                        className={styles.submitButton}
                                        style={{
                                            width: "fit-content",
                                            display: "flex",
                                            whiteSpace: "nowrap",
                                        }}
                                        disabled={
                                            authFormFields.resendOtp.timer > 0 &&
                                            !authFormFields.isLoading
                                        }
                                        onClick={handleResendOtp}
                                    >
                                        {authFormFields.resendOtp.timer > 0
                                            ? `Resend in ${authFormFields.resendOtp.timer} sec`
                                            : "Resend OTP"}
                                    </motion.button>
                                )}

                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    className={styles.submitButton}
                                    onClick={() => {
                                        handleSubmit();
                                    }}
                                    disabled={authFormFields.isLoading}
                                >
                                    {authFormFields.isLoading ? (
                                        <BeatLoader color="#fff" size={8} />
                                    ) : (
                                        authFormFields.primaryButtonText
                                    )}
                                </motion.button>
                            </div>
                            <>
                                <div className={styles.orContainer}>
                                    <div className={styles.line}></div>
                                    <div className={styles.or}>OR</div>
                                    <div className={styles.line}></div>
                                </div>
                                <div className={styles.formAltLoginContainer}>
                                    <div
                                        className={`pointer ${styles.googleIcon}`}
                                        onClick={() => handleGoogleLogin()}
                                    >
                                        <FaGoogle />
                                        Continue with Google
                                    </div>
                                </div>
                            </>
                        </motion.div>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default Authentication;
