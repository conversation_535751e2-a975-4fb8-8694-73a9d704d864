import { generateOTP, login, preRegister, registerUser, resetUserPassword } from "../../apis/auth";
import { AuthFormFields } from "./types";
import { NavigateFunction } from "react-router";

export function handleSubmitLogic({
    authFormFields,
    setAuthFormFields,
    emailRef,
    nameRef,
    otpRef,
    passwordRef,
    navigate,
}: {
    authFormFields: AuthFormFields;
    setAuthFormFields: React.Dispatch<React.SetStateAction<AuthFormFields>>;
    emailRef: React.RefObject<HTMLInputElement>;
    nameRef: React.RefObject<HTMLInputElement>;
    otpRef: React.RefObject<HTMLInputElement>;
    passwordRef: React.RefObject<HTMLInputElement>;
    navigate: NavigateFunction;
}) {
    if (emailRef.current?.value === "") {
        setAuthFormFields((prev) => ({
            ...prev,
            email: {
                error: ["Email is required"],
                showField: true,
            },
        }));
        return;
    } else {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(emailRef.current?.value as string)) {
            setAuthFormFields((prev) => ({
                ...prev,
                email: {
                    error: ["Invalid Email"],
                    showField: true,
                },
            }));
            return;
        }
    }
    if (authFormFields.email.showField && emailRef.current?.value === "") {
        setAuthFormFields((prev) => ({
            ...prev,
            email: {
                error: ["Email is required"],
                showField: true,
            },
        }));
        return;
    }
    if (authFormFields.name.showField && nameRef.current?.value === "") {
        setAuthFormFields((prev) => ({
            ...prev,
            name: {
                error: ["Name is required"],
                showField: true,
            },
        }));
        return;
    }
    if (authFormFields.otp.showField && otpRef.current?.value === "") {
        setAuthFormFields((prev) => ({
            ...prev,
            otp: {
                error: ["OTP is required"],
                showField: true,
            },
        }));
        return;
    }
    if (authFormFields.password.showField && passwordRef.current?.value === "") {
        setAuthFormFields((prev) => ({
            ...prev,
            password: {
                error: ["Password is required"],
                showField: true,
            },
        }));
        return;
    }
    if (authFormFields.api.name === "GenerateOTP" && authFormFields.api.type) {
        generateOTP({
            email: emailRef.current?.value as string,
            type: authFormFields.api.type,
            setAuthFormFields,
        });
    } else if (authFormFields.api.name === "Login") {
        login({
            email: emailRef.current?.value as string,
            setAuthFormFields,
            otp: otpRef.current?.value as string,
            password: passwordRef.current?.value as string,
        });
    } else if (authFormFields.api.name === "PreRegister") {
        preRegister({
            email: emailRef.current?.value as string,
            setAuthFormFields,
        });
    } else if (authFormFields.api.name === "Register") {
        registerUser({
            email: emailRef.current?.value as string,
            name: nameRef.current?.value as string,
            otp: otpRef.current?.value as string,
            setAuthFormFields,
        });
    } else if (authFormFields.api.name === "ResetPassword") {
        resetUserPassword({
            email: emailRef.current?.value as string,
            otp: otpRef.current?.value as string,
            password: passwordRef.current?.value as string,
            setAuthFormFields,
            navigate,
        });
    }
}

export function handleResendOtpLogic({
    authFormFields,
    setAuthFormFields,
    emailRef,
}: {
    authFormFields: AuthFormFields;
    setAuthFormFields: React.Dispatch<React.SetStateAction<AuthFormFields>>;
    emailRef: React.RefObject<HTMLInputElement>;
}) {
    if (
        authFormFields.resendOtp.apiName === "GenerateOTP" &&
        authFormFields.resendOtp.apiType
    ) {
        generateOTP({
            email: emailRef.current?.value as string,
            type: authFormFields.resendOtp.apiType,
            setAuthFormFields,
        });
    }
    if (authFormFields.resendOtp.apiName === "PreRegister") {
        preRegister({
            email: emailRef.current?.value as string,
            setAuthFormFields,
        });
    }
}
