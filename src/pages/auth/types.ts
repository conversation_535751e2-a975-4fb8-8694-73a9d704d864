export type errorType = {
  email?: string;
  password?: string;
  otp?: string;
  name?: string;
};

export type AuthForm<PERSON>ield = {
  error: string[];
  showField: boolean;
  message?: string;
};

export type AuthFormFields = {
  email: AuthForm<PERSON>ield;
  name: Auth<PERSON><PERSON><PERSON><PERSON>;
  otp: AuthForm<PERSON>ield;
  password: AuthForm<PERSON>ield;
  api: {
    name: string;
    type: string | null;
  };
  resendOtp: {
    apiName: string;
    apiType: string | null;
    showButton: boolean;
    timer: number;
  };
  primaryButtonText: string;
  generalMessage?: string;
  authenticated?: boolean;
  loginType?: 'password' | 'otp';
  isLoading?: boolean;
};
