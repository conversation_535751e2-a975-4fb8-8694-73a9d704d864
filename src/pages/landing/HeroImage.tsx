import { motion } from "framer-motion";
import styles from "./Landing.module.css";

const imageVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
        opacity: 1,
        scale: 1,
        transition: {
            duration: 0.8,
            ease: [0.6, -0.05, 0.01, 0.99],
        },
    },
};

export function HeroImage() {
    return (
        <motion.div className={styles.imageContainer} variants={imageVariants}>
            <div className={styles.imageWrapper}>
                <motion.div
                    className={styles.imageBg}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 0.1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                />
                <motion.img
                    src="/assets/heroimg.webp"
                    alt="People collaborating at an event"
                    className={styles.heroImage}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                />
                <p className={styles.imageHelperText}>
                    Guess what?, they are voting using jusvote.
                </p>
            </div>
        </motion.div>
    );
}
