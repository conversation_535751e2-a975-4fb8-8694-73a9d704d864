import { motion } from "framer-motion";
import styles from "./Landing.module.css";
import { HeroTitle } from "./HeroTitle";
import { HeroImage } from "./HeroImage";
import { BiMessageSquare } from "react-icons/bi";
import Navbar from "../../components/Navbar/Navbar";
import Footer from "../../components/Footer/Footer";

function Landing() {
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2,
                delayChildren: 0.3,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: [0.6, -0.05, 0.01, 0.99],
            },
        },
    };

    return (
        <>
            <div className={styles.navbarContainer}>
                <Navbar showMMP={true} showLogout={false} showName={false}/>
            </div>
            <motion.section
                className={styles.hero}
                initial="hidden"
                animate="visible"
                variants={containerVariants}
            >
                <motion.div className={styles.content} variants={itemVariants}>
                    <p className={styles.branding}>
                        from the makers of{" "}
                        <a href="https://makemypass.com">makemypass.com</a>
                    </p>
                    <HeroTitle />
                    <p className={styles.description}>
                        Streamline your event voting process with our real-time
                        voting system. Perfect for hackathons, pitching
                        competitions, and ideathons. Make every vote count,
                        instantly.
                    </p>
                    <div className={styles.buttons}>
                        <motion.a
                            href="https://wa.me/916238450178"
                            target="_blank"
                            rel="noreferrer"
                            className={styles.cta}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            >
                            <BiMessageSquare size={18} />
                            Contact Team
                        </motion.a>
                        <motion.a
                            href="/login"
                            className={styles.cta}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            >
                            Get Started
                        </motion.a>
                    </div>
                </motion.div>
                <HeroImage />
            </motion.section>
            <br />
            <Footer />
        </>
    );
}

export default Landing;
