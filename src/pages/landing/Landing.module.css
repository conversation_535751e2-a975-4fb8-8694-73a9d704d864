.hero {
    max-width: 1300px;
    margin: 0 auto;
    min-height: calc(100vh - 134.18px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6rem;
    padding: 0 2rem;
}

.content {
    flex: 1;
    max-width: 35rem;
}

.title {
    font-size: 3.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.highlight {
    color: var(--accent);
    position: relative;
    display: inline-block;
}

.highlightBg {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 0.3em;
    background: var(--accent);
    opacity: 0.2;
    z-index: -1;
}

.description {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    max-width: 28rem;
}

.cta {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, #333 0%, #555 100%);
    color: #fff;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9375rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.imageContainer {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.imageWrapper {
    position: relative;
    width: 100%;
    max-width: 480px;
}

.heroImage {
    width: 100%;
    height: auto;
    object-fit: cover;
    object-position: right;
    border-radius: 16px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.imageHelperText {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    text-align: center;
}

.imageBg {
    position: absolute;
    top: 3rem;
    right: -2rem;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent) 0%, #00cc66 100%);
    border-radius: 16px;
    opacity: 0.1;
    z-index: -1;
}

.buttons{
    margin: 2rem 0;
    display: flex;
    gap: 1rem;
}

@media (max-width: 1024px) {
    .hero {
        flex-direction: column;
        text-align: center;
        gap: 4rem;
        padding-top: 2rem;
    }

    .content {
        order: 1;
        max-width: 100%;
    }

    .description {
        margin-left: auto;
        margin-right: auto;
    }

    .imageContainer {
        order: 2;
        padding: 0 1rem;
    }

    .title {
        font-size: 2.75rem;
    }

    .imageBg {
        right: -1rem;
        top: 2rem;
    }
}

@media (max-width: 768px) {
    .title {
        font-size: 2.5rem;
    }

    .description {
        font-size: 0.9375rem;
    }

    .cta {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2.25rem;
    }

    .description {
        font-size: 0.875rem;
    }

    .cta {
        padding: 0.75rem 1.25rem;
        font-size: 0.8125rem;
    }
}

.navbarContainer {
    max-width: 1300px;
    margin: 0 auto;
    padding: 1rem 2rem;
    padding-bottom: 0;
}

.branding {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.branding a {
    color: var(--accent);
    text-decoration: none;
    font-weight: 600;
}

.branding a:hover {
    text-decoration: underline;
}
