import { motion } from "framer-motion";
import styles from "./Landing.module.css";

export function HeroTitle() {
    return (
        <h1 className={styles.title}>
            Real-time{" "}
            <span className={styles.highlight}>
                Voting
                <motion.span
                    className={styles.highlightBg}
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 0.8, delay: 0.5 }}
                />
            </span>{" "}
            Platform for Your Next Big Event
        </h1>
    );
}
