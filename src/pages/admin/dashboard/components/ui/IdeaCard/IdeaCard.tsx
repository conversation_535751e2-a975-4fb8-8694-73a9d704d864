import { useEffect, useRef } from "react";
import { BsCheckCircle, BsMegaphone, BsX } from "react-icons/bs";
import { HiDotsVertical } from "react-icons/hi";
import ModeMenu from "../../modal/ModeMenu/ModeMenu";
import styles from "./IdeaCard.module.css";
import { Team } from "../../../../../../types/idea";
import { useNavigate } from "react-router-dom";
import { getModeStyle, getModeCardStyle } from "./functions";

interface IdeaCardProps {
    idea: Team;
    onModeChange: (id: number, mode: Team["status"]) => void;
    isMenuOpen: boolean;
    onMenuToggle: (id: number) => void;
    onClearMode: (id: number) => void;
    eventName: string;
    teams: Team[];
}

export default function IdeaCard({
    idea,
    onModeChange,
    isMenuOpen,
    onMenuToggle,
    onClearMode,
    eventName,
    teams,
}: IdeaCardProps) {
    const navigate = useNavigate();
    const menuRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                onMenuToggle(idea.id);
            }
        };

        if (isMenuOpen) {
            document.addEventListener("mousedown", handleClickOutside);
        } else {
            document.removeEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [isMenuOpen, onMenuToggle, idea.id]);

    return (
        <div className={`${styles.card} ${getModeCardStyle(idea, styles)}`}>
            <div className={styles.header}>
                <div className={styles.teamCardHeading}>
                    {idea.logo && typeof idea.logo === "string" && (
                        <img src={idea.logo} alt="" className={styles.teamCardLogo} />
                    )}
                    <div className={styles.teamCardTexts}>
                        <h2 className={styles.title}>
                            {idea.team_name}
                            {idea.sector && (
                                <span className={styles.ideaSectorPill}>{idea.sector}</span>
                            )}
                        </h2>
                        <p className={styles.teamCode}>{idea.team_code}</p>
                    </div>
                </div>

                {(teams.some((team) => team.status !== "Idle" && team.id === idea.id) ||
                    teams.every((team) => team.status === "Idle")) && (
                    <button
                        className={styles.menuButton}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onMenuToggle(idea.id);
                        }}
                    >
                        <HiDotsVertical size={16} />
                    </button>
                )}

                {isMenuOpen && (
                    <div ref={menuRef} className={styles.menuContainer}>
                        <ModeMenu
                            ideaId={idea.id}
                            onModeChange={onModeChange}
                            currentMode={idea.status}
                        />
                    </div>
                )}
            </div>
            <div className={styles.modeContainer}>
                <div className={`${styles.mode} ${getModeStyle(idea, styles)}`}>
                    Status:{" "}
                    {idea.status === "Voting" ? (
                        <>
                            <BsCheckCircle size={12} />
                            <span>Voting</span>
                        </>
                    ) : idea.status === "Presenting" ? (
                        <>
                            <BsMegaphone size={12} />
                            <span>Presenting</span>
                        </>
                    ) : (
                        <>
                            <span>Idle</span>
                        </>
                    )}
                </div>
                {idea.status && idea.status !== "Idle" && (
                    <button
                        className={styles.clearButton}
                        onClick={(e) => {
                            e.stopPropagation();
                            onClearMode(idea.id);
                        }}
                        title="Clear mode"
                    >
                        <BsX size={16} />
                    </button>
                )}

                {idea.already_voted && (
                    <p className={styles.alreadyVoted} title="Atleast one poll has been received">
                        Voting Initated
                    </p>
                )}
            </div>
            <button
                className={styles.viewResultsButton}
                onClick={() => navigate(`/${eventName}/admin/idea/${idea.id}`)}
            >
                View Results
            </button>
        </div>
    );
}
