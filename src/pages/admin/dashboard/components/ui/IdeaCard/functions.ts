import { Team } from "../../../../../../types/idea";

export function getModeStyle(idea: Team, styles: any) {
    if (idea.status === "Idle") return styles.inactive;
    return styles[idea.status];
}

export function getModeCardStyle(idea: Team, styles: any) {
    if (idea.status === "Idle") return styles.idleCard;
    if (idea.status === "Voting") return styles.votingCard;
    if (idea.status === "Presenting") return styles.presentingCard;
} 