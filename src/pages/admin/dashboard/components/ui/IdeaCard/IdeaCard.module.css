.card {
    background: white;
    border-radius: 0.5rem;
    padding: 1rem;
    position: relative;
    border: 1px solid #eee;
    transition: border-color 0.2s ease;
    cursor: pointer;
    width: fit-content;
    min-width: 250px;
}

.menuContainer {
    position: absolute;
    top: 0;
    right: 0;
}

.card:hover {
    border-color: #ddd;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}

.title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #333132;
    margin: 0;
}

.teamCode {
    margin-top: 0.5rem;
}

.menuButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    color: #666;
}

.menuButton:hover {
    background-color: #f5f5f5;
}

.scoreContainer {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.score {
    font-size: 1.25rem;
    font-weight: 500;
    color: #333132;
}

.scoreLabel {
    font-size: 0.75rem;
    color: #666;
}

.mode {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.Presenting {
    background-color: #f3f9ff;
    color: #1976d2;
}

.Voting {
    background-color: #fff3f7;
    color: #c2185b;
}

.inactive {
    background-color: #f5f5f5;
    color: #666;
}

.ideaSectorPill {
    padding: 0.1rem 0.25rem;
    border-radius: 0.25rem;
    background-color: #f0f0f0;
    color: #333;
    font-weight: bold;
    text-align: center;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.teamCardLogo {
    width: 3rem;
    border-radius: 8px;
    object-fit: cover;
}

.teamCardHeading {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    gap: 0.5rem;
}

.clearButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
    color: #666;
    display: inline-flex;
    align-items: center;
}

.clearButton:hover {
    color: #d32f2f;
}

.modeContainer {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.teamCardHeading {
    font-size: 0.65rem;
    font-weight: 500;
    color: #333132;
    margin: 0;
}

.viewResultsButton {
    background-color: #333132;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s;
    border: none;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.idleCard {
    background-color: #fff;
    color: #666;
    border: 1px solid #ddd;
}

.presentingCard {
    background-color: #f3f9ff;
    color: #1976d2;
    border: 1px solid #1976d2;
}

.votingCard {
    background-color: #fff3f7;
    color: #c2185b;
    border: 1px solid #c2185b;
}

.alreadyVoted {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background-color: #e0e0e0;
    color: #333;
    font-size: 0.65rem;
    font-weight: 500;
    text-align: center;
}
