import { BsCheckCircle, BsMegaphone } from "react-icons/bs";
import styles from "./ModeMenu.module.css";
import { Team } from "../../../../../../types/idea";

interface ModeMenuProps {
    ideaId: number;
    onModeChange: (id: number, mode: Team["status"]) => void;
    currentMode: Team["status"];
}

export default function ModeMenu({
    ideaId,
    onModeChange,
    currentMode,
}: ModeMenuProps) {
    return (
        <div className={styles.menu}>
            <div
                className={`${styles.menuItem} ${
                    currentMode === "Voting" ? styles.active : ""
                }`}
                onClick={() => {
                    if (currentMode !== "Voting")
                        onModeChange(ideaId, "Voting");
                }}
            >
                <BsCheckCircle size={12} />
                <span>Voting Mode</span>
            </div>
            <div
                className={`${styles.menuItem} ${
                    currentMode === "Presenting" ? styles.active : ""
                }`}
                onClick={() => {
                    if (currentMode !== "Presenting")
                        onModeChange(ideaId, "Presenting");
                }}
            >
                <BsMegaphone size={12} />
                <span>Presenting Mode</span>
            </div>
        </div>
    );
}
