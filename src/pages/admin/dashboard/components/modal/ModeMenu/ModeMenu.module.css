.menu {
    position: absolute;
    top: 2.5rem;
    right: 1rem;
    background: white;
    border-radius: 0.25rem;
    border: 1px solid #eee;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;
  }
  
  .menuItem {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #333;
    white-space: nowrap;
  }
  
  .menuItem:hover {
    background-color: #f5f5f5;
  }
  
  .menuItem.active {
    background-color: #f5f5f5;
    font-weight: 500;
  }
  
  .menuItem:first-child {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  
  .menuItem:last-child {
    border-bottom-left-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
  }