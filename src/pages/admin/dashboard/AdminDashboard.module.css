.container {
    background-color: #fbfbf5;
    min-height: calc(100vh - 3.3rem);
    padding: 1rem;
}

.dashboard {
    max-width: 1300px;
    margin: 0 auto;
}

.header {
    font-size: 1.5rem;
    color: #333132;
    font-weight: 500;
    margin-bottom: 1.5rem;
    text-align: center;
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.loaderContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 60vh;
    position: relative;
}

.statusHeading {
    font-size: 1.25rem;
    color: #333132;
    font-weight: 600;
    margin-bottom: 0.25rem;
    margin-top: 1rem;
}

.statusDescription {
    font-size: 0.9rem;
    color: #333132;
    font-weight: 400;
    margin-bottom: 1rem;
    margin-top: 0.25rem;
}

.statusHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
}

.timerPill {
    background-color: #e0e0e0;
    border-radius: 999px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #333132;
    display: inline-block;
    text-align: center;
}

.modal {
    max-width: 400px;
    width: 100%;
    text-align: flex-start;
}

.modal h2 {
    font-size: 1.15rem;
    margin-bottom: 1rem;
}

.modal input {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 0.25rem;
    font-size: 1rem;
}

.modal button {
    width: 100%;
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.modal button:hover {
    background: #494748;
}

.viewLeaderboardButton {
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem;
    font-size: 0.875rem; /* Reduced font size */
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease, transform 0.2s ease; /* Added transform transition */
    margin-top: 1rem;

    position: absolute;
    top: 0;
    right: 0;
}

.viewLeaderboardButton:hover {
    background: #494748;
    transform: scale(1.05); /* Added scale animation on hover */
}

.teamListingContainer {
    position: relative;
}

.backButton {
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem; /* Reduced font size */
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease, transform 0.2s ease; /* Added transform transition */
}

.backButtonContainer p {
    font-size: 1.5rem;
    color: #333132;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.backButtonContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 1rem;
    flex-direction: row;
    gap: 0.5rem;
}

.searchContainer {
    display: flex;
    justify-content: flex-start;
}

.searchInput {
    width: 100%;
    max-width: 300px;
    padding: 0.4rem;
    border: 1px solid #ccc;
    border-radius: 0.25rem;
    font-size: 1rem;
    margin-top: 1rem;
}

.noTeamsFound {
    font-size: 1.25rem;
    color: #333132;
    font-weight: 500;
    text-align: center;
    margin-top: 2rem;
}