import { Dispatch, SetStateAction } from "react";
import { listTeams, updateTeamStatus } from "../../../apis/admin";
import { getEventId } from "../../../apis/user";
import { Mode, Team } from "../../../types/idea";
import { statusDescription } from "./constants";

export function useTeams(eventId: string | undefined, setTeams: Dispatch<SetStateAction<Team[]>>, setIsLoading: Dispatch<SetStateAction<boolean>>) {
    if (eventId) listTeams(eventId, setTeams, setIsLoading);
}

export function useEventId(eventName: string | undefined, setEventId: Dispatch<SetStateAction<string | undefined>>) {
    if (eventName) getEventId(eventName, setEventId);
}

export function getActiveMode(teams: Team[]): Mode {
    const activeIdea = teams.find((idea) => idea.status !== "Idle");
    return activeIdea?.status ?? "Idle";
}

export function handleMenuToggle(openMenuId: number | null, setOpenMenuId: Dispatch<SetStateAction<number | null>>, id: number) {
    setOpenMenuId(openMenuId === id ? null : id);
}

export function handleModeChange(id: number, newMode: Mode, teams: Team[], setTeams: Dispatch<SetStateAction<Team[]>>, setOpenMenuId: Dispatch<SetStateAction<number | null>>) {
    if (getActiveMode(teams) && getActiveMode(teams) !== teams.find((i) => i.id === id)?.status) {
        return;
    }
    const teamId = teams.find((i) => i.id === id)?.id;
    if (teamId && newMode) {
        updateTeamStatus(teamId.toString(), newMode, teams, setTeams, id);
    }
    setOpenMenuId(null);
}

export function handleClearMode(id: number, teams: Team[], setTeams: Dispatch<SetStateAction<Team[]>>) {
    setTeams(teams.map((idea) => (idea.id === id ? { ...idea, status: "Idle" } : idea)));
    const teamId = teams.find((i) => i.id === id)?.id;
    if (teamId) {
        updateTeamStatus(teamId.toString(), "Idle", teams, setTeams, id);
    }
}

export function formatEventName(eventName: string | undefined) {
    return eventName
        ?.replace(/_/g, " ")
        .replace(/^[a-z]/, (c) => c.toUpperCase());
}

export { statusDescription };

export function filterTeams(teams: Team[], status: Mode, searchQuery: string) {
    return teams.filter(
        (idea) =>
            idea.status === status &&
            (!idea.already_voted || status !== "Idle") &&
            (idea.team_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                idea.team_code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                idea.sector?.toLowerCase().includes(searchQuery.toLowerCase()))
    );
}

export function filterCompletedTeams(teams: Team[], searchQuery: string) {
    return teams.filter(
        (idea) =>
            idea.status === "Idle" &&
            idea.already_voted &&
            (idea.team_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                idea.team_code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                idea.sector?.toLowerCase().includes(searchQuery.toLowerCase()))
    );
}

export function filterAllTeams(teams: Team[], searchQuery: string) {
    return teams.filter(
        (idea) =>
            idea.team_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            idea.team_code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            idea.sector?.toLowerCase().includes(searchQuery.toLowerCase())
    );
}
