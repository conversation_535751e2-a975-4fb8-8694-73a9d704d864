import { useEffect, useState } from "react";
import styles from "./AdminDashboard.module.css";
import { Team } from "../../../types/idea";
import IdeaCard from "./components/ui/IdeaCard/IdeaCard";
import Navbar from "../../../components/Navbar/Navbar";
import Footer from "../../../components/Footer/Footer";
import { HashLoader } from "react-spinners";
import { useNavigate, useParams } from "react-router-dom";
import { IoIosArrowBack } from "react-icons/io";
// import Modal from "../../components/Modal/Modal";
import {
    useTeams,
    useEventId,
    handleMenuToggle,
    handleModeChange,
    handleClearMode,
    formatEventName,
    statusDescription,
    filterTeams,
    filterCompletedTeams,
    filterAllTeams
} from "./functions";

export default function AdminDashboard() {
    const [teams, setTeams] = useState<Team[]>([]);
    const [openMenuId, setOpenMenuId] = useState<number | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [eventId, setEventId] = useState<string>();
    const [searchQuery, setSearchQuery] = useState<string>("")
    const { eventName } = useParams<{ eventName: string }>();

    const navigate = useNavigate();

    useEffect(() => {
        useTeams(eventId, setTeams, setIsLoading);
    }, [eventId]);

    useEffect(() => {
        useEventId(eventName, setEventId);
    }, [eventName]);

    return (
        <>
            <div className={styles.container}>
                <div className={styles.dashboard}>
                    <Navbar />
                    {isLoading ? (
                        <>
                            <div className={styles.loaderContainer}>
                                <HashLoader size={50} />
                            </div>
                        </>
                    ) : (
                        <div className={styles.teamListingContainer}>
                            <button
                                className={styles.viewLeaderboardButton}
                                onClick={() => navigate(`/${eventName}/admin/leaderboard`)}
                            >
                                View Leaderboard
                            </button>
                            <div className={styles.backButtonContainer}>
                                <IoIosArrowBack
                                    color="#333132"
                                    onClick={() => navigate(-1)}
                                    size={25}
                                />
                                <p>
                                    {formatEventName(eventName)}
                                </p>
                            </div>

                            <div className={styles.searchContainer}>
                                <input
                                    type="text"
                                    placeholder="Search Teams"
                                    className={styles.searchInput}
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                            </div>

                            {(["Voting", "Presenting", "Idle"] as const).map((status) => {
                                const filteredTeams = filterTeams(teams, status, searchQuery);
                                return filteredTeams?.length > 0 ? (
                                    <div key={status}>
                                        <div className={styles.statusHeader}>
                                            <p className={styles.statusHeading}>{status} Teams</p>
                                        </div>
                                        <p className={styles.statusDescription}>
                                            {statusDescription[status]}
                                        </p>
                                        <div className={styles.grid}>
                                            {filteredTeams.map((idea) => (
                                                <IdeaCard
                                                    key={idea.id}
                                                    idea={idea}
                                                    onModeChange={(id, mode) => handleModeChange(id, mode, teams, setTeams, setOpenMenuId)}
                                                    isMenuOpen={openMenuId === idea.id}
                                                    onMenuToggle={(id) => handleMenuToggle(openMenuId, setOpenMenuId, id)}
                                                    onClearMode={(id) => handleClearMode(id, teams, setTeams)}
                                                    eventName={eventName ?? ""}
                                                    teams={teams}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                ) : null;
                            })}

                            {filterCompletedTeams(teams, searchQuery)?.length > 0 && (
                                <div>
                                    <div className={styles.statusHeader}>
                                        <p className={styles.statusHeading}>Completed Teams</p>
                                    </div>
                                    <p className={styles.statusDescription}>
                                        Voting has already initated for these teams
                                    </p>
                                    <div className={styles.grid}>
                                        {filterCompletedTeams(teams, searchQuery).map((idea) => (
                                            <IdeaCard
                                                key={idea.id}
                                                idea={idea}
                                                onModeChange={(id, mode) => handleModeChange(id, mode, teams, setTeams, setOpenMenuId)}
                                                isMenuOpen={openMenuId === idea.id}
                                                onMenuToggle={(id) => handleMenuToggle(openMenuId, setOpenMenuId, id)}
                                                onClearMode={(id) => handleClearMode(id, teams, setTeams)}
                                                eventName={eventName ?? ""}
                                                teams={teams}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}

                            {filterAllTeams(teams, searchQuery)?.length === 0 && (
                                <div className={styles.noTeamsFound}>
                                    {searchQuery === "" ? (
                                        <p>No teams found</p>
                                    ) : (
                                        <p>
                                            No teams found matching your search query. Try searching
                                            for a different team.
                                        </p>
                                    )}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
            <Footer />
        </>
    );
}
