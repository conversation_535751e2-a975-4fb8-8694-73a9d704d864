.backgroundContainer {
    background-color: #fbfbf5;
    min-height: calc(100vh - 3.3rem);
    padding: 1rem;
}

.outerContainer {
    max-width: 1300px;
    margin: 0 auto;
    height: 100%;
}

.dashboardContainer {
    padding: 2rem;
}

.pageHeader {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333132;
}

.pageHeader span {
    color: #666;
}

.pageDescription {
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 2rem;
}

.eventCardContainer {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.eventCard {
    background-color: #333132;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.titleRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.eventName {
    font-size: 1rem;
    font-weight: 500;
    color: #f5f5f5;
}

.participantCount {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #f5f5f5;
}

.createEventButton {
    background-color: #f5f5f5;
    color: #333132;
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem 1.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    width: fit-content;
}

.createEventButton:hover {
    background-color: #fbfbf5;
}

.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.noEventsMessage {
    font-size: 0.9rem;
    color: #666;
    text-align: left;
    max-width: 30rem;
}

.noEventMessageContainer {
    text-align: center;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    border-radius: 8px;
    margin: 20px 0;
}

.noEventMessageContainer h2 {
    font-size: 24px;
    color: #343a40;
    margin-bottom: 10px;

    max-width: 35rem;
}

.noEventMessageContainer p {
    max-width: 35rem;
}
