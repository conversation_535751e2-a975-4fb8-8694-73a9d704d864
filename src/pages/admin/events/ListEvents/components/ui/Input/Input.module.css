.inputContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #f5f5f5;
}

.required {
    color: #ff6b6b;
    margin-left: 0.25rem;
}

.input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #444;
    border-radius: 0.5rem;
    background-color: #2a2a2a;
    color: #f5f5f5;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.input:focus {
    outline: none;
    border-color: #f5f5f5;
}

.input:disabled {
    background-color: #1a1a1a;
    color: #666;
    cursor: not-allowed;
}

.input.error {
    border-color: #ff6b6b;
}

.errorText {
    font-size: 0.75rem;
    color: #ff6b6b;
}
