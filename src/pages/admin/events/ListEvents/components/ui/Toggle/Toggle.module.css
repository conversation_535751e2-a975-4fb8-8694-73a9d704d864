.toggleContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.toggleHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #f5f5f5;
}

.toggle {
    position: relative;
    width: 3rem;
    height: 1.5rem;
    background-color: #444;
    border: none;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.toggle:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.toggle.checked {
    background-color: #f5f5f5;
}

.toggleSlider {
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.2s ease;
}

.toggle.checked .toggleSlider {
    transform: translateX(1.5rem);
    background-color: #333132;
}

.description {
    font-size: 0.75rem;
    color: #999;
    margin: 0;
}
