import React, { useState, useEffect } from "react";
import Modal from "../../../../../../../components/Modal/Modal";
import Input from "../../ui/Input/Input";
import Button from "../../ui/Button/Button";
import Toggle from "../../ui/Toggle/Toggle";
import { getEvent, updateEvent } from "../../../../../../../apis/admin";
import { Event, UpdateEventRequest } from "../../../../../../../types/event";
import { HashLoader } from "react-spinners";
import styles from "./UpdateEventModal.module.css";

interface UpdateEventModalProps {
    isOpen: boolean;
    onClose: () => void;
    eventId: string;
    onEventUpdated: (event: Event) => void;
}

const UpdateEventModal: React.FC<UpdateEventModalProps> = ({
    isOpen,
    onClose,
    eventId,
    onEventUpdated,
}) => {
    const [originalEvent, setOriginalEvent] = useState<Event | null>(null);
    const [formData, setFormData] = useState({
        name: "",
        public_voting: false,
        resubmission_allowed: false,
        is_admin_controlled: false,
        presentation_time_limit: 0,
        voting_time_limit: 0,
    });
    const [isLoading, setIsLoading] = useState(false);
    const [isFetching, setIsFetching] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});

    useEffect(() => {
        if (isOpen && eventId) {
            getEvent(eventId, setOriginalEvent, setIsFetching);
        }
    }, [isOpen, eventId]);

    useEffect(() => {
        if (originalEvent) {
            setFormData({
                name: originalEvent.name || "",
                public_voting: originalEvent.public_voting || false,
                resubmission_allowed: originalEvent.resubmission_allowed || false,
                is_admin_controlled: originalEvent.is_admin_controlled || false,
                presentation_time_limit: originalEvent.presentation_time_limit || 0,
                voting_time_limit: originalEvent.voting_time_limit || 0,
            });
        }
    }, [originalEvent]);

    const validateForm = () => {
        const newErrors: Record<string, string> = {};
        
        if (!formData.name.trim()) {
            newErrors.name = "Event name is required";
        }
        
        if (formData.presentation_time_limit < 0) {
            newErrors.presentation_time_limit = "Time limit cannot be negative";
        }
        
        if (formData.voting_time_limit < 0) {
            newErrors.voting_time_limit = "Time limit cannot be negative";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const getDiff = (): UpdateEventRequest => {
        if (!originalEvent) return {};
        
        const diff: UpdateEventRequest = {};
        
        if (formData.name !== originalEvent.name) {
            diff.name = formData.name;
        }
        if (formData.public_voting !== originalEvent.public_voting) {
            diff.public_voting = formData.public_voting;
        }
        if (formData.resubmission_allowed !== originalEvent.resubmission_allowed) {
            diff.resubmission_allowed = formData.resubmission_allowed;
        }
        if (formData.is_admin_controlled !== originalEvent.is_admin_controlled) {
            diff.is_admin_controlled = formData.is_admin_controlled;
        }
        if (formData.presentation_time_limit !== originalEvent.presentation_time_limit) {
            diff.presentation_time_limit = formData.presentation_time_limit;
        }
        if (formData.voting_time_limit !== originalEvent.voting_time_limit) {
            diff.voting_time_limit = formData.voting_time_limit;
        }
        
        return diff;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        const diff = getDiff();
        
        // If no changes, just close the modal
        if (Object.keys(diff).length === 0) {
            handleClose();
            return;
        }

        updateEvent(
            eventId,
            diff,
            setIsLoading,
            (event) => {
                onEventUpdated(event);
                handleClose();
            }
        );
    };

    const handleClose = () => {
        setOriginalEvent(null);
        setFormData({
            name: "",
            public_voting: false,
            resubmission_allowed: false,
            is_admin_controlled: false,
            presentation_time_limit: 0,
            voting_time_limit: 0,
        });
        setErrors({});
        setIsLoading(false);
        setIsFetching(false);
        onClose();
    };

    if (!isOpen) return null;

    return (
        <Modal title="Edit Event" onClose={handleClose}>
            {isFetching ? (
                <div className={styles.loader}>
                    <HashLoader size={30} color="#f5f5f5" />
                    <p>Loading event details...</p>
                </div>
            ) : (
                <form onSubmit={handleSubmit} className={styles.form}>
                    <Input
                        label="Event Name"
                        value={formData.name}
                        onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
                        placeholder="Enter event name"
                        required
                        error={errors.name}
                        disabled={isLoading}
                    />

                    <div className={styles.row}>
                        <Input
                            label="Presentation Time Limit (minutes)"
                            value={formData.presentation_time_limit}
                            onChange={(value) => setFormData(prev => ({ 
                                ...prev, 
                                presentation_time_limit: parseInt(value) || 0 
                            }))}
                            type="number"
                            placeholder="0"
                            error={errors.presentation_time_limit}
                            disabled={isLoading}
                        />
                        <Input
                            label="Voting Time Limit (minutes)"
                            value={formData.voting_time_limit}
                            onChange={(value) => setFormData(prev => ({ 
                                ...prev, 
                                voting_time_limit: parseInt(value) || 0 
                            }))}
                            type="number"
                            placeholder="0"
                            error={errors.voting_time_limit}
                            disabled={isLoading}
                        />
                    </div>

                    <Toggle
                        label="Public Voting"
                        checked={formData.public_voting}
                        onChange={(checked) => setFormData(prev => ({ ...prev, public_voting: checked }))}
                        description="Allow public users to vote on this event"
                        disabled={isLoading}
                    />

                    <Toggle
                        label="Resubmission Allowed"
                        checked={formData.resubmission_allowed}
                        onChange={(checked) => setFormData(prev => ({ ...prev, resubmission_allowed: checked }))}
                        description="Allow teams to resubmit their votes"
                        disabled={isLoading}
                    />

                    <Toggle
                        label="Admin Controlled"
                        checked={formData.is_admin_controlled}
                        onChange={(checked) => setFormData(prev => ({ ...prev, is_admin_controlled: checked }))}
                        description="Only admins can control voting and presentation modes"
                        disabled={isLoading}
                    />
                    
                    <div className={styles.buttonGroup}>
                        <Button
                            variant="secondary"
                            onClick={handleClose}
                            disabled={isLoading}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            loading={isLoading}
                            disabled={!formData.name.trim()}
                        >
                            Update Event
                        </Button>
                    </div>
                </form>
            )}
        </Modal>
    );
};

export default UpdateEventModal;
