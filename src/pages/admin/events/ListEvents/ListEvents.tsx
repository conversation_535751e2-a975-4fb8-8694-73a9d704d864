import styles from "./ListEvents.module.css";
import { useNavigate } from "react-router-dom";
import { GoAlertFill, GoPeople } from "react-icons/go";
import { useEffect, useState } from "react";
import { listEvents } from "../../../../apis/admin";
import { HashLoader } from "react-spinners";
import Navbar from "../../../../components/Navbar/Navbar";
import Footer from "../../../../components/Footer/Footer";

const ListEvents = () => {
    const navigate = useNavigate();

    const userName = localStorage.getItem("userEmail")?.split("@")[0];
    const [events, setEvents] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        listEvents(setEvents, setIsLoading);
    }, []);
    return (
        <>
            <div className={styles.backgroundContainer}>
                <Navbar />
                <div className={styles.outerContainer}>
                    <div className={styles.dashboardContainer}>
                        {userName && (
                            <p className={styles.pageHeader}>
                                <span>Hi,</span> {userName}
                            </p>
                        )}
                        <p className={styles.pageDescription}>
                            Welcome to the dashboard! Here you can manage your events.
                        </p>

                        {!isLoading && events && events?.length === 0 && (
                            <div className={styles.noEventMessageContainer}>
                                <GoAlertFill size={35} />
                                <>
                                    <h2>No events available </h2>
                                    <p className={styles.subText}>
                                        There aren't any events available for you to manage. Kindly
                                        create an event to get started.
                                    </p>
                                </>
                            </div>
                        )}

                        {!isLoading ? (
                            <div className={styles.eventCardContainer}>
                                {Array.isArray(events) &&
                                    events.map((event) => (
                                        <div key={event.id} className={styles.eventCard}>
                                            <div className={styles.titleRow}>
                                                <p className={styles.eventName}>
                                                    {event?.name?.length > 15
                                                        ? `${event.name.substring(0, 15)}...`
                                                        : event.name}
                                                </p>
                                                <p className={styles.participantCount}>
                                                    <GoPeople color="#fff" size={15} />{" "}
                                                    {event.team_count} Teams
                                                </p>
                                            </div>

                                            <button
                                                className={styles.createEventButton}
                                                onClick={() => {
                                                    navigate(`/${event.name}/admin`);
                                                }}
                                            >
                                                Manage
                                            </button>
                                        </div>
                                    ))}
                            </div>
                        ) : (
                            <div className={styles.loader}>
                                <HashLoader size={50} />
                            </div>
                        )}
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default ListEvents;
