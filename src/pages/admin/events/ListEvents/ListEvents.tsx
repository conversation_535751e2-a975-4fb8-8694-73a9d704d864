import styles from "./ListEvents.module.css";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>lertFill, GoPeople, GoPlus, GoPencil, GoTrash } from "react-icons/go";
import { useEffect, useState } from "react";
import { listEvents } from "../../../../apis/admin";
import { HashLoader } from "react-spinners";
import Navbar from "../../../../components/Navbar/Navbar";
import Footer from "../../../../components/Footer/Footer";
import { Event } from "../../../../types/event";
import CreateEventModal from "./components/modal/CreateEventModal/CreateEventModal";
import UpdateEventModal from "./components/modal/UpdateEventModal/UpdateEventModal";
import DeleteConfirmationModal from "./components/modal/DeleteConfirmationModal/DeleteConfirmationModal";
import Button from "./components/ui/Button/Button";

const ListEvents = () => {
    const navigate = useNavigate();

    const userName = localStorage.getItem("userEmail")?.split("@")[0];
    const [events, setEvents] = useState<Event[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    // Modal states
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

    useEffect(() => {
        fetchEvents();
    }, []);

    const fetchEvents = () => {
        listEvents(setEvents, setIsLoading);
    };

    const handleEventCreated = (newEvent: Event) => {
        console.log("Adding new event to list:", newEvent);
        setEvents(prev => {
            console.log("Previous events:", prev);
            const result = Array.isArray(prev) ? [...prev, newEvent] : [newEvent];
            console.log("New events list:", result);
            return result;
        });
    };

    const handleEventUpdated = (updatedEvent: Event) => {
        setEvents(prev => Array.isArray(prev) ? prev.map(event =>
            event.id === updatedEvent.id ? updatedEvent : event
        ) : [updatedEvent]);
    };

    const handleEventDeleted = (eventId: string) => {
        setEvents(prev => Array.isArray(prev) ? prev.filter(event => event.id !== eventId) : []);
    };

    const handleEditClick = (event: Event) => {
        setSelectedEvent(event);
        setIsUpdateModalOpen(true);
    };

    const handleDeleteClick = (event: Event) => {
        setSelectedEvent(event);
        setIsDeleteModalOpen(true);
    };
    return (
        <>
            <div className={styles.backgroundContainer}>
                <Navbar />
                <div className={styles.outerContainer}>
                    <div className={styles.dashboardContainer}>
                        {userName && (
                            <p className={styles.pageHeader}>
                                <span>Hi,</span> {userName}
                            </p>
                        )}
                        <div className={styles.headerRow}>
                            <p className={styles.pageDescription}>
                                Welcome to the dashboard! Here you can manage your events.
                            </p>
                            <Button
                                onClick={() => setIsCreateModalOpen(true)}
                                size="medium"
                            >
                                <GoPlus size={16} />
                                Create Event
                            </Button>
                        </div>

                        {!isLoading && Array.isArray(events) && events.length === 0 && (
                            <div className={styles.noEventMessageContainer}>
                                <GoAlertFill size={35} />
                                <>
                                    <h2>No events available </h2>
                                    <p className={styles.subText}>
                                        There aren't any events available for you to manage. Kindly
                                        create an event to get started.
                                    </p>
                                </>
                            </div>
                        )}

                        {!isLoading ? (
                            <div className={styles.eventCardContainer}>
                                {Array.isArray(events) && events.length > 0 &&
                                    events.map((event) => (
                                        <div key={event.id} className={styles.eventCard}>
                                            <div className={styles.titleRow}>
                                                <p className={styles.eventName}>
                                                    {event?.name?.length > 15
                                                        ? `${event.name.substring(0, 15)}...`
                                                        : event.name}
                                                </p>
                                                <p className={styles.participantCount}>
                                                    <GoPeople color="#fff" size={15} />{" "}
                                                    {event.team_count || 0} Teams
                                                </p>
                                            </div>

                                            <div className={styles.cardActions}>
                                                <div className={styles.actionButtons}>
                                                    <button
                                                        className={styles.actionButton}
                                                        onClick={() => handleEditClick(event)}
                                                        title="Edit Event"
                                                    >
                                                        <GoPencil size={14} />
                                                    </button>
                                                    <button
                                                        className={styles.actionButton}
                                                        onClick={() => handleDeleteClick(event)}
                                                        title="Delete Event"
                                                    >
                                                        <GoTrash size={14} />
                                                    </button>
                                                </div>
                                                <button
                                                    className={styles.createEventButton}
                                                    onClick={() => {
                                                        navigate(`/${event.name}/admin`);
                                                    }}
                                                >
                                                    Manage
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                            </div>
                        ) : (
                            <div className={styles.loader}>
                                <HashLoader size={50} />
                            </div>
                        )}
                    </div>
                </div>
            </div>
            <Footer />

            {/* Modals */}
            <CreateEventModal
                isOpen={isCreateModalOpen}
                onClose={() => setIsCreateModalOpen(false)}
                onEventCreated={handleEventCreated}
            />

            <UpdateEventModal
                isOpen={isUpdateModalOpen}
                onClose={() => setIsUpdateModalOpen(false)}
                eventId={selectedEvent?.id || ""}
                onEventUpdated={handleEventUpdated}
            />

            <DeleteConfirmationModal
                isOpen={isDeleteModalOpen}
                onClose={() => setIsDeleteModalOpen(false)}
                event={selectedEvent}
                onEventDeleted={handleEventDeleted}
            />
        </>
    );
};

export default ListEvents;
