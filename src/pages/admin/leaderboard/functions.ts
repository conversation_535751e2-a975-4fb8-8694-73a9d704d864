// handleSort function
export function handleSort(
  column: string,
  sortColumn: string,
  sortDirection: "asc" | "desc",
  data: any[],
  setSortColumn: (col: string) => void,
  setSortDirection: (dir: "asc" | "desc") => void,
  setData: (data: any[]) => void
) {
  const direction = sortColumn === column && sortDirection === "desc" ? "asc" : "desc";
  const sorted = [...data].sort((a, b) => {
    const valA = column === "team_name" || column === "team_code" ? a[column] : a[column] || 0;
    const valB = column === "team_name" || column === "team_code" ? b[column] : b[column] || 0;
    if (direction === "asc") return valA > valB ? 1 : -1;
    return valA < valB ? 1 : -1;
  });
  setData(sorted);
  setSortColumn(column);
  setSortDirection(direction);
}

// getSortIcon function
export function getSortIcon(
  column: string,
  sortColumn: string,
  sortDirection: "asc" | "desc"
) {
  if (sortColumn !== column) return "↕";
  return sortDirection === "asc" ? "▲" : "▼";
}

// filterLeaderboard function
export function filterLeaderboard(
  leaderboard: any[],
  searchQuery: string
) {
  const lowerCaseQuery = searchQuery?.toLowerCase();
  return leaderboard.filter(
    (team) =>
      team.team_name?.toLowerCase().includes(lowerCaseQuery) ||
      team.team_code?.toLowerCase().includes(lowerCaseQuery) ||
      Object.values(team.question_points).some((value) =>
        String(value).toString()?.toLowerCase().includes(lowerCaseQuery)
      )
  );
}
