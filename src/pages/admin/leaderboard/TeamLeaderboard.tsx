import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { getEventId } from "../../../apis/user";
import { getLeaderboard } from "../../../apis/admin";
import styles from "./TeamLeaderboard.module.css";
import Navbar from "../../../components/Navbar/Navbar";
import Footer from "../../../components/Footer/Footer";
import { IoIosArrowBack } from "react-icons/io";
import { handleSort, getSortIcon, filterLeaderboard } from "./functions";

const TeamLeaderboard = () => {
    const [leaderboard, setLeaderboard] = useState<any[]>([]);
    const [filteredLeaderboard, setFilteredLeaderboard] = useState<any[]>([]);
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [sortColumn, setSortColumn] = useState<string>("total_points");
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [eventId, setEventId] = useState<string>();

    const { eventName } = useParams<{ eventName: string }>();

    useEffect(() => {
        if (eventId) {
            setIsLoading(true);
            getLeaderboard(eventId, setLeaderboard, setIsLoading, setFilteredLeaderboard);
        }
    }, [eventId]);

    useEffect(() => {
        if (eventName) getEventId(eventName, setEventId);
    }, [eventName]);

    useEffect(() => {
        setFilteredLeaderboard(filterLeaderboard(leaderboard, searchQuery));
    }, [searchQuery, leaderboard]);

    const navigate = useNavigate();

    const questionHeaders = Object.keys(filteredLeaderboard[0]?.question_points || {});

    return (
        <>
            <div className={styles.backgroundContainer}>
                <Navbar showLogout={true} />
                <div className={styles.backButtonContainer}>
                    <IoIosArrowBack color="#333132" onClick={() => navigate(-1)} size={25} />
                    <p>{eventName?.replace(/_/g, " ").replace(/^\w/, (c) => c.toUpperCase())}</p>
                </div>
                <div className={styles.outerContainer}>
                    {isLoading ? (
                        <div className={styles.loaderContainer}>Loading...</div>
                    ) : (
                        <div className={styles.dashboardContainer}>
                            <h1 className={styles.title}>Team Leaderboard</h1>
                            <input
                                type="text"
                                placeholder="Search by team name, code, or points..."
                                className={styles.searchInput}
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <div className={styles.scrollContainer}>
                                <table className={styles.leaderboardTable}>
                                    <thead className={styles.tableHeader}>
                                        <tr>
                                            <th onClick={() => handleSort("rank", sortColumn, sortDirection, filteredLeaderboard, setSortColumn, setSortDirection, setFilteredLeaderboard)}>
                                                Rank {getSortIcon("rank", sortColumn, sortDirection)}
                                            </th>
                                            <th onClick={() => handleSort("team_name", sortColumn, sortDirection, filteredLeaderboard, setSortColumn, setSortDirection, setFilteredLeaderboard)}>
                                                Team {getSortIcon("team_name", sortColumn, sortDirection)}
                                            </th>
                                            <th
                                                className={styles.rightAlign}
                                                onClick={() => handleSort("total_points", sortColumn, sortDirection, filteredLeaderboard, setSortColumn, setSortDirection, setFilteredLeaderboard)}
                                            >
                                                Total {getSortIcon("total_points", sortColumn, sortDirection)}
                                            </th>
                                            {questionHeaders.map((header) => (
                                                <th
                                                    key={header}
                                                    className={styles.rightAlign}
                                                    onClick={() => handleSort(header, sortColumn, sortDirection, filteredLeaderboard, setSortColumn, setSortDirection, setFilteredLeaderboard)}
                                                >
                                                    {header} {getSortIcon(header, sortColumn, sortDirection)}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredLeaderboard.map((team, index) => (
                                            <tr key={team.team_id} className={styles.tableRow}>
                                                <td
                                                    className={`${styles.tableCell} ${styles.rankCell}`}
                                                >
                                                    {index + 1}
                                                </td>
                                                <td className={styles.tableCell}>
                                                    <div>{team.team_code}</div>
                                                    <div
                                                        style={{
                                                            fontSize: "0.75rem",
                                                            color: "#666",
                                                        }}
                                                    >
                                                        {team.team_name}
                                                    </div>
                                                </td>
                                                <td
                                                    className={`${styles.tableCell} ${styles.rightAlign} ${styles.totalPoints}`}
                                                >
                                                    {team.total_points.toFixed(1)}
                                                </td>
                                                {questionHeaders.map((header) => (
                                                    <td
                                                        key={header}
                                                        className={`${styles.tableCell} ${styles.rightAlign} ${styles.scoreCell}`}
                                                    >
                                                        {team.question_points[header].toFixed(1)}
                                                    </td>
                                                ))}
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <Footer />
        </>
    );
};

export default TeamLeaderboard;
