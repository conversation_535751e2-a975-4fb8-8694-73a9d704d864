.backgroundContainer {
    background-color: #fbfbf5;
    min-height: calc(100vh - 3.3rem);
    padding: 1rem;
}

.outerContainer {
    max-width: 1300px;
    margin: 0 auto;
}

.dashboardContainer {
    background-color: white;
    padding: 2rem;
}

.title {
    font-size: 1.5rem;
    color: #333132;
    font-weight: 500;
    margin-bottom: 2rem;
    text-align: center;
}

.leaderboardTable {
    width: 100%;
    border-collapse: collapse;
}

.tableHeader th {
    padding: 0.75rem;
    font-weight: 500;
    color: #333132;
    text-align: left;
    border-bottom: 1px solid #eeeee7;
    font-size: 0.875rem;
    white-space: nowrap;
}

.tableRow {
    border-bottom: 1px solid #eeeee7;
}

.tableCell {
    padding: 0.75rem;
    color: #333132;
    font-size: 0.875rem;
}

.rankCell {
    color: #666;
}

.scoreCell {
    background-color: #fafafa;
    font-size: 0.75rem;
    color: #666;
}

.rightAlign {
    text-align: left;
}

.scrollContainer {
    overflow-x: auto;
}

.loaderContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60vh;
}

.totalPoints {
    font-weight: 500;
}

.searchInput {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}

.tableHeader th {
    cursor: pointer;
}

.backButton {
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem; /* Reduced font size */
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease, transform 0.2s ease; /* Added transform transition */
}

.backButtonContainer p {
    font-size: 1.5rem;
    color: #333132;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.backButtonContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 1rem;
    flex-direction: row;
    gap: 0.5rem;
    margin-bottom: 1rem;
}
