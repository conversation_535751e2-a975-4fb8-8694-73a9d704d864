import { PollResult } from "../../../../../types/poll";
import ResultsHeader from "./ResultsHeader";
import ResultsRow from "./ResultsRow";
import styles from "./ResultsTable.module.css";

interface ResultsTableProps {
    polls: PollResult[] | undefined;
}

export default function ResultsTable({ polls }: ResultsTableProps) {
    return (
        <div className={styles.container}>
            <table className={styles.table}>
                <ResultsHeader polls={polls} />
                <tbody>
                    {polls &&
                        polls.map((result, index) => (
                            <ResultsRow key={index} result={result} index={index} polls={polls} />
                        ))}
                </tbody>
            </table>
        </div>
    );
}
