import { PollResult } from "../../../../../types/poll";
import { calculateTotalScore } from "../../../../../utils/pollResults";
import styles from "./ResultsTable.module.css";

interface ResultsRowProps {
    result: PollResult;
    index: number;
    polls: PollResult[] | undefined;
}

export default function ResultsRow({ result, index, polls }: ResultsRowProps) {
    const totalScore = calculateTotalScore(result);
    return (
        <tr className={styles.tr}>
            <td className={`${styles.td} ${styles.serialNumber}`}>{index + 1}</td>

            {polls &&
                Object.keys(polls[0]).map((question, qIndex) => (
                    (question !== "feedback" &&
                    question !== "invest_amount") && <td key={qIndex} className={`${styles.td} ${styles.score}`}>
                        {(result[question] as number) || "-"}
                    </td>
                ))}

            <td className={`${styles.td} ${styles.totalScore}`}>
                {totalScore > 0 ? totalScore : "-"}
            </td>
        </tr>
    );
}
