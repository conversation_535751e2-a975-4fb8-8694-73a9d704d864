import { PollResult } from "../../../../../types/poll";
import styles from "./ResultsTable.module.css";

export default function ResultsHeader({ polls }: { polls: PollResult[] | undefined }) {
    return (
        <thead className={styles.thead}>
            <tr>
                <th className={`${styles.th} ${styles.serialNumber}`}>Sl.No</th>

                {polls &&
                    Object.keys(polls[0]).map(
                        (question, index) =>
                            question !== "feedback" &&
                            question !== "invest_amount" && (
                                <th key={index} className={styles.th}>
                                    {question}
                                </th>
                            )
                    )}

                <th className={styles.th}>Total</th>
            </tr>
        </thead>
    );
}
