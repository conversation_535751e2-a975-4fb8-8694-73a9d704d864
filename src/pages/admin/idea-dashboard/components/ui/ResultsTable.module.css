.container {
    max-width: 1300px;
    margin: 2rem auto;
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.thead {
    background: #333132;
    color: white;
    position: sticky;
    top: 0;
}

.th {
    padding: 1rem;
    text-align: left;
    font-size: 0.875rem;
    font-weight: 500;
    max-width: 13rem;
}

.maxScore {
    font-size: 0.75rem;
    opacity: 0.8;
}

.td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    font-size: 0.875rem;
    color: #333132;
}

.serialNumber {
    width: 4rem;
    color: #666;
}

.score {
    font-weight: 500;
    text-align: center;
}

.feedback {
    max-width: 20rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.feedbackCell {
    max-width: 20rem;
}

.totalScore {
    font-weight: 600;
    color: #333132;
    text-align: center;
}

.tr:hover {
    background-color: #f9f9f9;
}

.emptyState {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 0.875rem;
}
