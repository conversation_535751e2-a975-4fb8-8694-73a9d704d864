import { useEffect, useState } from "react";
import styles from "./IdeaDashboard.module.css";
import ResultsTable from "./components/ui/ResultsTable";
import { PollResult } from "../../../types/poll";
import Navbar from "../../../components/Navbar/Navbar";
import Footer from "../../../components/Footer/Footer";
import { useNavigate, useParams } from "react-router-dom";
import { HashLoader } from "react-spinners";
import { IoIosArrowBack } from "react-icons/io";
import { useEventId, usePolls, formatEventName } from "./functions";

const IdeaDashboard = () => {
    const [polls, setPolls] = useState<PollResult[]>();
    const { ideaId } = useParams<{ ideaId: string }>();
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [eventId, setEventId] = useState<string>();
    const { eventName } = useParams<{ eventName: string }>();
    const navigate = useNavigate();

    useEffect(() => {
        useEventId(eventName, setEventId);
    }, [eventName]);

    useEffect(() => {
        usePolls(eventId, ideaId, setPolls, setIsLoading);
    }, [eventId, ideaId]);

    return (
        <>
            <div className={styles.backgroundContainer}>
                <div className={styles.container}>
                    <Navbar />
                    {!isLoading ? (
                        <>
                            <div className={styles.backButtonContainer}>
                                <IoIosArrowBack
                                    color="#333132"
                                    onClick={() => navigate(-1)}
                                    size={25}
                                />
                                <p>
                                    {formatEventName(eventName)}
                                </p>
                            </div>
                            {polls?.length !== 0 ? (
                                <ResultsTable polls={polls} />
                            ) : (
                                <div className={styles.noPolls}>
                                    <p className={styles.noPollsErrorHeading}>
                                        No polls available for this idea
                                    </p>
                                    <p className={styles.noPollErrorSubHeading}>
                                        There are no polls available for this team, please check
                                        back later.
                                    </p>
                                </div>
                            )}
                        </>
                    ) : (
                        <div className={styles.loader}>
                            <HashLoader size={50} />
                        </div>
                    )}
                </div>
            </div>
            <Footer />
        </>
    );
};

export default IdeaDashboard;
