.backgroundContainer {
    background-color: #fbfbf5;
    min-height: calc(100vh - 3.3rem);
    padding: 1rem;
}

.container {
    max-width: 1300px;
    margin: 0 auto;
    height: 100%;
}

.noPollsErrorHeading {
    font-size: 1.25rem;
    color: #333132;
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-align: center;
}

.noPollsErrorSubHeading {
    font-size: 1rem;
    color: #666;
    text-align: center;
}

.noPolls {
    min-height: 75vh;
    text-align: center;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 60vh;
}

.backButton {
    background: #333132;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem; /* Reduced font size */
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease, transform 0.2s ease; /* Added transform transition */
}

.backButtonContainer p {
    font-size: 1.5rem;
    color: #333132;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.backButtonContainer {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 1rem;
    flex-direction: row;
    gap: 0.5rem;
}
