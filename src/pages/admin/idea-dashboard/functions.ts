import { getEventId } from "../../../apis/user";
import { getPolls } from "../../../apis/admin";
import { PollResult } from "../../../types/poll";
import { Dispatch, SetStateAction } from "react";

export function useEventId(eventName: string | undefined, setEventId: Dispatch<SetStateAction<string | undefined>>) {
    // Handles fetching eventId when eventName changes
    if (eventName) getEventId(eventName, setEventId);
}

export function usePolls(eventId: string | undefined, ideaId: string | undefined, setPolls: Dispatch<SetStateAction<PollResult[] | undefined>>, setIsLoading: Dispatch<SetStateAction<boolean>>) {
    // Handles fetching polls when eventId or ideaId changes
    if (ideaId && eventId) getPolls(eventId, ideaId, setPolls, setIsLoading);
}

export function formatEventName(eventName: string | undefined) {
    return eventName
        ?.replace(/_/g, " ")
        .replace(/^[a-z]/, (c) => c.toUpperCase());
}
