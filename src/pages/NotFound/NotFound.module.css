.container {
    background-color: #fbfbf5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.content {
    max-width: 35rem;
    width: 100%;
    min-height: 79vh;
    height: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border-radius: 0.5rem;
}

.errorCode {
    margin-bottom: 1.5rem;
}

.errorCode span {
    font-size: 4rem;
    font-weight: 700;
    color: #111827;
}

.title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.description {
    color: #4b5563;
    margin-bottom: 2rem;
    max-width: 28rem;
}

@media (min-width: 640px) {
    .errorCode span {
        font-size: 6rem;
    }

    .title {
        font-size: 1.875rem;
    }
}